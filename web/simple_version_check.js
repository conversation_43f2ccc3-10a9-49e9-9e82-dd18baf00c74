// 簡化版本檢查腳本 - 解決閃爍和卡住問題

(function() {
  'use strict';

  const CURRENT_VERSION = '{{BUILD_VERSION}}';
  const CHECK_INTERVAL = 120000; // 2分鐘檢查一次，提高響應速度
  let isChecking = false;
  let hasNotified = false;
  let lastNotifiedVersion = null; // 記錄上次通知的版本
  let checkCount = 0; // 檢查次數計數器

  console.log('🚀 Version checker initialized. Current version:', CURRENT_VERSION);

  // 從 localStorage 讀取上次通知的版本
  try {
    lastNotifiedVersion = localStorage.getItem('lastNotifiedVersion');
    console.log('📝 Last notified version:', lastNotifiedVersion);
  } catch (e) {
    console.log('⚠️ 無法讀取 localStorage:', e);
  }
  
  // 檢查是否需要顯示更新通知
  function shouldShowNotification(serverVersion) {
    // 如果版本相同，不需要通知
    if (serverVersion === CURRENT_VERSION) {
      return false;
    }

    // 如果已經為這個版本通知過，不再通知
    if (lastNotifiedVersion === serverVersion) {
      return false;
    }

    // 檢查版本是否有意義的更新（避免僅時間戳變化的通知）
    // 如果服務器版本包含特殊標記（如 'update' 或版本號格式），才顯示通知
    if (serverVersion && serverVersion.includes('update')) {
      return true;
    }

    // 檢查是否為語義化版本號（x.y.z 格式）
    const semanticVersionRegex = /^\d+\.\d+\.\d+/;
    if (semanticVersionRegex.test(serverVersion) && semanticVersionRegex.test(CURRENT_VERSION)) {
      return compareSemanticVersions(serverVersion, CURRENT_VERSION) > 0;
    }

    // 對於時間戳版本，檢查時間差是否超過閾值（避免頻繁的小更新通知）
    const timestampRegex = /^\d{8}_\d{6}$/;
    if (timestampRegex.test(serverVersion) && timestampRegex.test(CURRENT_VERSION)) {
      return shouldNotifyForTimestampVersion(serverVersion, CURRENT_VERSION);
    }

    return false;
  }

  // 比較語義化版本號
  function compareSemanticVersions(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }

  // 判斷時間戳版本是否需要通知
  function shouldNotifyForTimestampVersion(serverVersion, currentVersion) {
    try {
      const serverDate = parseTimestampVersion(serverVersion);
      const currentDate = parseTimestampVersion(currentVersion);

      if (!serverDate || !currentDate) return false;

      // 只有當服務器版本比當前版本新超過 1 小時才通知
      const timeDiff = serverDate.getTime() - currentDate.getTime();
      return timeDiff > 3600000; // 1 小時 = 3600000 毫秒
    } catch (e) {
      console.log('時間戳版本解析錯誤:', e);
      return false;
    }
  }

  // 解析時間戳版本號
  function parseTimestampVersion(version) {
    const match = version.match(/^(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})$/);
    if (!match) return null;

    const [, year, month, day, hour, minute, second] = match;
    return new Date(
      parseInt(year),
      parseInt(month) - 1, // 月份從 0 開始
      parseInt(day),
      parseInt(hour),
      parseInt(minute),
      parseInt(second)
    );
  }

  // 簡單的版本檢查
  function checkVersion() {
    if (isChecking || hasNotified) {
      return;
    }

    isChecking = true;
    checkCount++;

    console.log(`🔍 Checking version (attempt ${checkCount})...`);

    // 使用更強的快取破壞策略
    const cacheBuster = Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    fetch(`/version.json?_=${cacheBuster}`, {
      method: 'GET',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('📊 Server version:', data.version, 'Current:', CURRENT_VERSION);
      console.log('📊 Server data:', data);

      if (data.version && shouldShowNotification(data.version)) {
        console.log('🔔 Showing update notification for version:', data.version);
        showSimpleNotification(data.version, data);
      } else {
        console.log('✅ No update needed');
      }
    })
    .catch(error => {
      console.log('❌ Version check error:', error);
    })
    .finally(() => {
      isChecking = false;
    });
  }
  
  // 顯示簡單通知
  function showSimpleNotification(serverVersion, serverData = {}) {
    if (hasNotified) return;
    hasNotified = true;

    console.log('🔔 Displaying update notification for version:', serverVersion);

    // 記錄已通知的版本
    try {
      localStorage.setItem('lastNotifiedVersion', serverVersion);
      lastNotifiedVersion = serverVersion;
    } catch (e) {
      console.log('⚠️ 無法寫入 localStorage:', e);
    }

    // 移除舊通知
    const oldNotification = document.getElementById('version-notification');
    if (oldNotification) {
      oldNotification.remove();
    }

    // 判斷是否為重要更新
    const isImportantUpdate = serverData.updateType === 'important' ||
                             serverVersion.includes('update') ||
                             serverData.forceCacheClear === true;
    
    const notification = document.createElement('div');
    notification.id = 'version-notification';

    // 根據更新類型選擇不同的樣式和文字
    const bgColor = isImportantUpdate ? '#FF5722' : '#2196F3';
    const title = isImportantUpdate ? '🔥 重要更新可用' : '🚀 新版本可用';
    const message = isImportantUpdate ?
      '此更新包含重要改進，建議立即更新' :
      '重新載入以獲得最新功能';

    // 生成唯一的按鈕 ID
    const timestamp = Date.now();
    const updateBtnId = `update-btn-${timestamp}`;
    const dismissBtnId = `dismiss-btn-${timestamp}`;

    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        z-index: 999999;
        font-family: system-ui, -apple-system, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
      ">
        <style>
          @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        </style>
        <div style="font-weight: bold; margin-bottom: 8px;">
          ${title}
        </div>
        <div style="margin-bottom: 12px; opacity: 0.9; font-size: 13px;">
          ${message}
        </div>
        <div style="margin-bottom: 12px; opacity: 0.8; font-size: 12px;">
          版本: ${serverVersion}
        </div>
        <div style="display: flex; gap: 8px;">
          <button id="${updateBtnId}" style="
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            flex: 1;
            min-height: 40px;
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            transition: transform 0.1s ease;
            outline: none;
            position: relative;
            overflow: hidden;
          ">立即更新</button>
          ${!isImportantUpdate ? `
          <button id="${dismissBtnId}" style="
            background: transparent;
            color: white;
            border: 1px solid rgba(255,255,255,0.5);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            flex: 1;
            min-height: 40px;
            -webkit-appearance: none;
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            transition: transform 0.1s ease;
            outline: none;
            position: relative;
            overflow: hidden;
          ">稍後</button>
          ` : ''}
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // 檢測是否為 PWA 環境
    const isPWA = window.matchMedia && window.matchMedia('(display-mode: standalone)').matches;
    console.log('📱 PWA environment detected:', isPWA);

    // 為按鈕添加事件監聽器（iOS Safari PWA 觸控優化）
    // 使用 setTimeout 確保 DOM 元素已經添加
    setTimeout(() => {
      // 更新按鈕事件處理
      const updateBtn = document.getElementById(updateBtnId);
      if (updateBtn) {
        let isProcessing = false; // 防止重複觸發

        const handleUpdate = (e) => {
          if (isProcessing) return;
          isProcessing = true;

          e.preventDefault();
          e.stopPropagation();
          console.log('🔄 Update button triggered, PWA:', isPWA);

          // PWA 環境中添加額外的觸控反饋
          if (isPWA) {
            updateBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
              updateBtn.style.transform = 'scale(1)';
            }, 100);
          }

          reloadPage(serverData.forceCacheClear || false);

          // 重置處理狀態
          setTimeout(() => {
            isProcessing = false;
          }, 1000);
        };

        // 添加多種事件監聽器
        updateBtn.addEventListener('click', handleUpdate, { passive: false });
        updateBtn.addEventListener('touchstart', handleUpdate, { passive: false });
        updateBtn.addEventListener('touchend', handleUpdate, { passive: false });

        // PWA 專用：添加指針事件
        if (isPWA) {
          updateBtn.addEventListener('pointerdown', handleUpdate, { passive: false });
          updateBtn.addEventListener('pointerup', handleUpdate, { passive: false });
        }
      }

      // 稍後按鈕事件處理
      if (!isImportantUpdate) {
        const dismissBtn = document.getElementById(dismissBtnId);
        if (dismissBtn) {
          let isProcessing = false;

          const handleDismiss = (e) => {
            if (isProcessing) return;
            isProcessing = true;

            e.preventDefault();
            e.stopPropagation();
            console.log('⏰ Dismiss button triggered, PWA:', isPWA);

            // PWA 環境中添加額外的觸控反饋
            if (isPWA) {
              dismissBtn.style.transform = 'scale(0.95)';
              setTimeout(() => {
                dismissBtn.style.transform = 'scale(1)';
              }, 100);
            }

            dismissNotification();

            setTimeout(() => {
              isProcessing = false;
            }, 1000);
          };

          dismissBtn.addEventListener('click', handleDismiss, { passive: false });
          dismissBtn.addEventListener('touchstart', handleDismiss, { passive: false });
          dismissBtn.addEventListener('touchend', handleDismiss, { passive: false });

          if (isPWA) {
            dismissBtn.addEventListener('pointerdown', handleDismiss, { passive: false });
            dismissBtn.addEventListener('pointerup', handleDismiss, { passive: false });
          }
        }
      }
    }, 100); // 增加延遲確保 DOM 完全載入
  }
  
  // 重新載入頁面
  window.reloadPage = function(forceCacheClear = false) {
    console.log('🔄 Starting page reload, forceCacheClear:', forceCacheClear);

    const notification = document.getElementById('version-notification');
    if (notification) {
      notification.innerHTML = `
        <div style="
          position: fixed;
          top: 20px;
          right: 20px;
          background: #FF9800;
          color: white;
          padding: 16px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          z-index: 999999;
          font-family: system-ui, -apple-system, sans-serif;
          font-size: 14px;
          text-align: center;
        ">
          <div>🔄 正在更新應用...</div>
          <div style="font-size: 12px; margin-top: 4px; opacity: 0.8;">請稍候</div>
        </div>
      `;
    }

    // 清除快取並重新載入
    setTimeout(() => {
      Promise.all([
        // 清除所有快取
        'caches' in window ? caches.keys().then(names =>
          Promise.all(names.map(name => caches.delete(name)))
        ) : Promise.resolve(),

        // 如果是強制清除，也清除 localStorage（保留重要數據）
        forceCacheClear ? new Promise(resolve => {
          try {
            // 保存重要數據
            const importantData = {
              lastNotifiedVersion: localStorage.getItem('lastNotifiedVersion')
            };

            // 清除所有數據
            localStorage.clear();
            sessionStorage.clear();

            // 恢復重要數據
            if (importantData.lastNotifiedVersion) {
              localStorage.setItem('lastNotifiedVersion', importantData.lastNotifiedVersion);
            }
          } catch (e) {
            console.log('⚠️ 清除 storage 失敗:', e);
          }
          resolve();
        }) : Promise.resolve()
      ]).finally(() => {
        // 使用時間戳強制重新載入
        const timestamp = Date.now();
        window.location.href = window.location.origin + window.location.pathname + '?t=' + timestamp;
      });
    }, 800);
  };
  
  // 關閉通知
  window.dismissNotification = function() {
    const notification = document.getElementById('version-notification');
    if (notification) {
      notification.remove();
    }
    hasNotified = false;

    // 30分鐘後重新檢查（延長間隔避免頻繁打擾）
    setTimeout(() => {
      hasNotified = false;
    }, 1800000); // 30分鐘
  };
  
  // 開始檢查
  function startChecking() {
    // 首次檢查延遲30秒，更快響應
    setTimeout(checkVersion, 30000);

    // 定期檢查
    setInterval(checkVersion, CHECK_INTERVAL);

    // 頁面可見性變化時也檢查
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        setTimeout(checkVersion, 2000);
      }
    });
  }
  
  // 頁面載入完成後開始
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startChecking);
  } else {
    startChecking();
  }
  
  // 頁面重新獲得焦點時檢查
  window.addEventListener('focus', () => {
    setTimeout(checkVersion, 1000);
  });
  
})();
