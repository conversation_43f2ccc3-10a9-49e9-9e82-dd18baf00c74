import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../data/models/app_introduction_data.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../themes/app_theme.dart';

/// 應用程式介紹頁面
/// 在用戶首次啟動應用時顯示，介紹應用功能和特色
class AppIntroductionPage extends StatefulWidget {
  const AppIntroductionPage({super.key});

  @override
  State<AppIntroductionPage> createState() => _AppIntroductionPageState();
}

class _AppIntroductionPageState extends State<AppIntroductionPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  int _currentPage = 0;
  final List<IntroductionPageData> _pages = AppIntroductionConfig.getIntroductionPages();

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// 跳到下一頁
  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeIntroduction();
    }
  }

  /// 跳到上一頁
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 跳過介紹
  void _skipIntroduction() {
    logger.i('用戶跳過應用介紹流程');
    _completeIntroduction();
  }

  /// 跳轉到指定頁面
  void _goToPage(int pageIndex) {
    if (pageIndex >= 0 && pageIndex < _pages.length) {
      _pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      logger.i('跳轉到第 ${pageIndex + 1} 頁');
    }
  }

  /// 完成介紹流程
  Future<void> _completeIntroduction() async {
    try {
      // 標記首次啟動流程已完成
      await UserPreferences.markFirstTimeUserCompleted();
      
      logger.i('用戶完成應用介紹流程');
      
      if (mounted) {
        // 導航到模式選擇頁面
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    } catch (e) {
      logger.e('完成介紹流程失敗: $e');
      // 即使失敗也要導航，避免用戶卡住
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/mode-selection');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
        ),
        child: SafeArea(
          child: ResponsivePageWrapper(
            maxWidth: 600.0,
            child: Column(
              children: [
                // 頂部導航區域
                _buildTopNavigation(),
                
                // 主要內容區域
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                      // 重新播放動畫
                      _animationController.reset();
                      _animationController.forward();
                    },
                    itemCount: _pages.length,
                    itemBuilder: (context, index) {
                      return _buildIntroductionPage(_pages[index]);
                    },
                  ),
                ),
                
                // 底部導航區域
                _buildBottomNavigation(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 檢測是否為 PWA 環境
  bool get _isPWAEnvironment {
    if (!kIsWeb) return false;
    // 在 Web 環境中，PWA 模式通常有特定的特徵
    // 這裡使用簡單的檢測邏輯
    return kIsWeb;
  }

  /// PWA 專用按鈕處理
  void _handlePWAButtonPress(VoidCallback onPressed, String debugName) {
    logger.d('$debugName PWA button press handler triggered');

    // 立即執行
    onPressed();

    // PWA 環境中的額外保險觸發
    if (_isPWAEnvironment) {
      Future.delayed(const Duration(milliseconds: 100), () {
        logger.d('$debugName PWA delayed execution');
        // 這裡不再次執行 onPressed，避免重複操作
      });
    }
  }

  /// 構建觸控優化按鈕包裝器（簡化版）
  Widget _buildTouchOptimizedButton({
    required Widget child,
    required VoidCallback onPressed,
    required String debugName,
  }) {
    return GestureDetector(
      onTap: () {
        logger.d('$debugName tapped via GestureDetector');
        _handlePWAButtonPress(onPressed, debugName);
      },
      onTapDown: (details) {
        logger.d('$debugName tap down detected');
      },
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }

  /// 構建優化的按鈕（直接創建，避免嵌套）
  Widget _buildOptimizedButton({
    required String text,
    required VoidCallback onPressed,
    required Color backgroundColor,
    required String debugName,
    bool isOutlined = false,
  }) {
    return Container(
      width: double.infinity,
      height: 50,
      child: GestureDetector(
        onTap: () {
          logger.d('$debugName button tapped');
          _handlePWAButtonPress(onPressed, debugName);
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          decoration: BoxDecoration(
            color: isOutlined ? Colors.transparent : backgroundColor,
            border: isOutlined ? Border.all(color: backgroundColor, width: 2) : null,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                logger.d('$debugName InkWell tapped');
                _handlePWAButtonPress(onPressed, debugName);
              },
              borderRadius: BorderRadius.circular(25),
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isOutlined ? backgroundColor : Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 構建簡化的文字按鈕
  Widget _buildTextButton({
    required String text,
    required VoidCallback onPressed,
    required Color textColor,
    required String debugName,
  }) {
    return GestureDetector(
      onTap: () {
        logger.d('$debugName text button tapped');
        _handlePWAButtonPress(onPressed, debugName);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 構建頂部導航
  Widget _buildTopNavigation() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 返回按鈕（第一頁時隱藏）
          SizedBox(
            width: 80,
            child: _currentPage > 0
                ? _buildTextButton(
                    text: '上一步',
                    onPressed: _previousPage,
                    textColor: AppColors.royalIndigo,
                    debugName: 'Previous',
                  )
                : null,
          ),

          // 頁面指示器
          _buildPageIndicator(),

          // 跳過按鈕（最後一頁時隱藏）
          SizedBox(
            width: 60,
            child: _currentPage < _pages.length - 1
                ? _buildTextButton(
                    text: '跳過',
                    onPressed: _skipIntroduction,
                    textColor: AppColors.textMedium,
                    debugName: 'Skip',
                  )
                : null,
          ),
        ],
      ),
    );
  }

  /// 構建頁面指示器
  Widget _buildPageIndicator() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(_pages.length, (index) {
        final isActive = _currentPage == index;
        return GestureDetector(
          onTap: () {
            logger.d('Page indicator $index tapped');
            _goToPage(index);
          },
          behavior: HitTestBehavior.opaque,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 6),
            padding: const EdgeInsets.all(4), // 增加觸控區域
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: isActive ? 18 : 12, // 活動狀態稍微大一點
              height: isActive ? 18 : 12,
              decoration: BoxDecoration(
                color: isActive
                    ? _pages[_currentPage].primaryColor
                    : AppColors.textLight.withOpacity(0.5),
                shape: BoxShape.circle, // 關鍵：改成圓形
                boxShadow: isActive
                    ? [
                  BoxShadow(
                    color: _pages[_currentPage].primaryColor.withOpacity(0.4),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  )
                ]
                    : [],
              ),
              child: isActive
                  ? Center(
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
                  : null,
            ),
          ),
        );
      }),
    );
  }


  /// 構建介紹頁面內容
  Widget _buildIntroductionPage(IntroductionPageData pageData) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
          child: Column(
            children: [
              // 圖標區域
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: pageData.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: Icon(
                  pageData.icon,
                  size: 50,
                  color: pageData.primaryColor,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 標題
              Text(
                pageData.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: pageData.primaryColor,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // 副標題
              Text(
                pageData.subtitle,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textDark,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // 描述
              Text(
                pageData.description,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textMedium,
                  height: 1.5,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 功能列表
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: pageData.features.map((feature) {
                      return _buildFeatureCard(feature);
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建功能卡片
  Widget _buildFeatureCard(AppFeature feature) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 功能圖標
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: feature.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              feature.icon,
              size: 24,
              color: feature.color,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 功能內容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  feature.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  feature.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建底部導航
  Widget _buildBottomNavigation() {
    final isLastPage = _currentPage == _pages.length - 1;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Column(
        children: [
          // 主要按鈕
          _buildOptimizedButton(
            text: isLastPage ? '開始使用' : '下一步',
            onPressed: isLastPage ? _completeIntroduction : _nextPage,
            backgroundColor: _pages[_currentPage].primaryColor,
            debugName: isLastPage ? 'Complete' : 'Next',
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
