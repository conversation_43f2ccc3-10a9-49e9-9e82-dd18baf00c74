import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/unified_card.dart';
import '../starlight_chart_selection_page.dart';

/// Starlight 關係分析頁面
/// 提供完整的關係分析選項和詳細說明
class StarlightRelationshipAnalysisPage extends StatefulWidget {
  final BirthData? primaryPerson;

  const StarlightRelationshipAnalysisPage({
    super.key,
    this.primaryPerson,
  });

  @override
  State<StarlightRelationshipAnalysisPage> createState() =>
      _StarlightRelationshipAnalysisPageState();
}

class _StarlightRelationshipAnalysisPageState
    extends State<StarlightRelationshipAnalysisPage> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightCornsilk,
      appBar: AppBar(
        title: const Text(
          '關係分析',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        backgroundColor: AppColors.lightCornsilk,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textDark),
      ),
      body: ResponsivePageWrapper(
        maxWidth: 800.0, // 關係分析頁面適合中等寬度
        child: SingleChildScrollView(
          padding: ResponsiveUtils.getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // 頁面介紹
            _buildIntroductionSection(),
            const SizedBox(height: 20),

            // 關係分析類型
            _buildAnalysisTypesSection(),
            // const SizedBox(height: 10),

            // 使用說明
            _buildInstructionsSection(),

            const SizedBox(height: 50),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建介紹區塊
  Widget _buildIntroductionSection() {
    return UnifiedCard(
      child: Padding(
        padding: const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(0),
                  decoration: BoxDecoration(
                    color: AppColors.cosmicPurple.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.favorite,
                    color: AppColors.cosmicPurple,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '探索你的關係密碼',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '透過占星學了解你與他人的緣分與相處之道',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建分析類型區塊
  Widget _buildAnalysisTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '關係分析類型',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _getRelationshipAnalysisItems().length,
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            final item = _getRelationshipAnalysisItems()[index];
            return _buildAnalysisTypeCard(item);
          },
        ),
      ],
    );
  }

  /// 構建使用說明區塊
  Widget _buildInstructionsSection() {
    return UnifiedCard(
      child: Padding(
        padding: const EdgeInsets.only(right: 0, left: 0, top: 0, bottom: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '使用說明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '1. 選擇主要人物（通常是你自己）\n'
              '2. 點擊想要的分析類型\n'
              '3. 選擇第二個人物進行比較\n'
              '4. 查看詳細的關係分析報告',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 獲取關係分析項目
  List<Map<String, dynamic>> _getRelationshipAnalysisItems() {
    return [
      {
        'title': '緣分配對',
        'subtitle': '比較盤分析',
        'description': '比較兩人的星盤，看看你們在性格、價值觀、生活方式上的契合度。了解彼此的優勢互補和潛在挑戰。',
        'icon': Icons.people,
        'color': AppColors.cosmicPurple,
        'chartType': ChartType.synastry,
        'features': ['性格契合度', '價值觀比較', '溝通方式分析', '相處建議'],
      },
      {
        'title': '愛情密碼',
        'subtitle': '組合盤分析',
        'description': '將兩人的星盤合成一個新的星盤，揭示你們作為一對情侶或夫妻的整體能量和發展方向。',
        'icon': Icons.favorite_border,
        'color': AppColors.starlightAccent,
        'chartType': ChartType.composite,
        'features': ['關係本質', '共同目標', '挑戰與機會', '長期發展'],
      },
      {
        'title': '還原關係真實樣貌',
        'subtitle': '時空中點盤',
        'description': '代表兩人所呈現出來的實際關係是怎樣的，還原關係本身的真實樣貌。看關係未來發展，矛盾衝突的原因是什麼。',
        'icon': Icons.timeline,
        'color': AppColors.solarAmber,
        'chartType': ChartType.davison,
        'features': ['相遇意義', '關係使命', '時機分析', '命運連結'],
      },
      {
        'title': '揭示雙方真實感受',
        'subtitle': '馬盤分析',
        'description': '細緻地描繪出關係中雙方的心理感受，深入探索彼此內心的想法和情緒。',
        'icon': Icons.psychology,
        'color': AppColors.royalIndigo,
        'chartType': ChartType.marks,
        'features': ['靈魂連結', '內心需求', '深層動力', '精神契合'],
      },
    ];
  }

  /// 構建分析類型卡片
  Widget _buildAnalysisTypeCard(Map<String, dynamic> item) {
    return UnifiedCard(
      child: InkWell(
        onTap: () => _navigateToAnalysis(item),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(0),
          child: Row(
            children: [
              // 圖標區域
              Container(
                padding: const EdgeInsets.all(0),
                decoration: BoxDecoration(
                  color: item['color'].withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  item['icon'],
                  color: item['color'],
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // 內容區域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            item['title'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textDark,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: item['color'].withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            item['subtitle'],
                            style: TextStyle(
                              fontSize: 10,
                              color: item['color'],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      item['description'],
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                        height: 1.3,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 箭頭圖標
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 導航到分析
  void _navigateToAnalysis(Map<String, dynamic> item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StarlightChartSelectionPage(
          primaryPerson: widget.primaryPerson,
          chartType: item['chartType'],
          analysisTitle: item['title'],
          fromAnalysisPage: true,
        ),
      ),
    );
  }
}
