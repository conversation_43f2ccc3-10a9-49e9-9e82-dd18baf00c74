import 'dart:convert';

import 'package:flutter/material.dart';

import '../../../data/models/astrology/astro_event.dart';
import '../../../data/models/user/birth_data.dart';
import '../../../features/astrology/services/event_cache_service.dart';
import '../../../shared/utils/logger_utils.dart';
import '../../../shared/utils/user_preferences.dart';

/// 事件偵測設定對話框
class EventDetectionSettingsDialog extends StatefulWidget {
  final BirthData birthData;
  final VoidCallback? onSettingsChanged;

  const EventDetectionSettingsDialog({
    super.key,
    required this.birthData,
    this.onSettingsChanged,
  });

  @override
  State<EventDetectionSettingsDialog> createState() => _EventDetectionSettingsDialogState();
}

class _EventDetectionSettingsDialogState extends State<EventDetectionSettingsDialog> {
  bool _isLoading = false;
  Map<String, dynamic>? _cacheStats;
  int _cacheExpiryDays = 7; // 預設7天
  double _minimumEventScore = 20.0; // 預設最低分數
  Set<AstroEventType> _enabledEventTypes = {}; // 啟用的事件類型

  @override
  void initState() {
    super.initState();
    _loadCacheStats();
    _loadCacheExpirySettings();
    _loadEventDetectionSettings();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.settings),
          SizedBox(width: 8),
          Text('事件偵測設定'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCacheSection(),
            const SizedBox(height: 16),
            _buildEventTypeSection(),
            const SizedBox(height: 16),
            _buildPerformanceSection(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('關閉'),
        ),
      ],
    );
  }

  /// 建立快取管理區域
  Widget _buildCacheSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快取管理',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        if (_cacheStats != null) ...[
          _buildCacheStatsCard(),
          const SizedBox(height: 10),
        ],
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _clearCurrentUserCache,
                icon: _isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.delete_outline),
                label: const Text('清除此用戶快取'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _clearAllCache,
                icon: _isLoading 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.delete_forever),
                label: const Text('清除所有快取'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 建立快取統計卡片
  Widget _buildCacheStatsCard() {
    final stats = _cacheStats!;
    final totalItems = stats['totalItems'] as int;
    final totalSizeKB = (stats['totalSizeBytes'] as int) / 1024;
    final validItems = stats['validItems'] as int;
    final expiredItems = stats['expiredItems'] as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快取統計',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('總項目數: $totalItems'),
                Text('大小: ${totalSizeKB.toStringAsFixed(1)} KB'),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('有效項目: $validItems', style: const TextStyle(color: Colors.green)),
                Text('過期項目: $expiredItems', style: const TextStyle(color: Colors.orange)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 建立事件類型設定區域
  Widget _buildEventTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '事件類型',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        const Text(
          '選擇要偵測的事件類型：',
          style: TextStyle(fontSize: 14, color: Colors.grey),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: [
            _buildEventTypeChip('行運相位', Icons.sync, AstroEventType.transitAspect),
            _buildEventTypeChip('推運相位', Icons.trending_up, AstroEventType.progressionAspect),
            _buildEventTypeChip('太陽弧推運', Icons.wb_sunny, AstroEventType.solarArcAspect),
            _buildEventTypeChip('行星換座', Icons.swap_horiz, AstroEventType.planetSignChange),
            _buildEventTypeChip('行星換宮', Icons.home, AstroEventType.planetHouseChange),
          ],
        ),
      ],
    );
  }

  /// 建立事件類型選擇晶片
  Widget _buildEventTypeChip(String label, IconData icon, AstroEventType eventType) {
    final isEnabled = _enabledEventTypes.contains(eventType);

    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isEnabled,
      onSelected: (selected) {
        _toggleEventType(eventType, selected);
      },
    );
  }

  /// 建立效能設定區域
  Widget _buildPerformanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '效能設定',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 0),
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.speed),
          title: const Text('快取有效期'),
          subtitle: Text('$_cacheExpiryDays 天'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showCacheExpiryDialog(),
        ),
        ListTile(
          contentPadding: EdgeInsets.zero,
          leading: const Icon(Icons.filter_list),
          title: const Text('最低事件分數'),
          subtitle: Text('${_minimumEventScore.toInt()} 分'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showMinimumScoreDialog(),
        ),
      ],
    );
  }

  /// 載入快取統計
  Future<void> _loadCacheStats() async {
    try {
      final stats = await EventCacheService.getCacheStatistics();
      if (mounted) {
        setState(() {
          _cacheStats = stats;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入快取統計失敗: $e')),
        );
      }
    }
  }

  /// 清除當前用戶的快取
  Future<void> _clearCurrentUserCache() async {
    final confirmed = await _showConfirmDialog(
      '清除此用戶快取',
      '確定要清除此用戶的所有事件偵測快取嗎？\n\n這將會刪除該用戶的所有快取資料，下次分析時需要重新計算。',
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await EventCacheService.clearCacheForBirthData(widget.birthData);
      await _loadCacheStats();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已成功清除此用戶的快取'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onSettingsChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清除快取失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 清除所有快取
  Future<void> _clearAllCache() async {
    final confirmed = await _showConfirmDialog(
      '清除所有快取',
      '確定要清除所有用戶的事件偵測快取嗎？\n\n這是一個危險操作，將會刪除所有快取資料，所有用戶下次分析時都需要重新計算。',
      isDangerous: true,
    );

    if (!confirmed) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await EventCacheService.clearAllCache();
      await _loadCacheStats();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已成功清除所有快取'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onSettingsChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('清除快取失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 顯示確認對話框
  Future<bool> _showConfirmDialog(String title, String content, {bool isDangerous = false}) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isDangerous ? Icons.warning : Icons.help_outline,
              color: isDangerous ? Colors.red : Colors.orange,
            ),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: isDangerous ? Colors.red : Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('確定'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 載入快取有效期設定
  Future<void> _loadCacheExpirySettings() async {
    try {
      final expiryDays = await UserPreferences.getEventCacheExpiryDays();
      if (mounted) {
        setState(() {
          _cacheExpiryDays = expiryDays;
        });
      }
    } catch (e) {
      logger.e('載入快取有效期設定失敗: $e');
    }
  }

  /// 保存快取有效期設定
  Future<void> _saveCacheExpirySettings(int days) async {
    try {
      await UserPreferences.saveEventCacheExpiryDays(days);

      // 更新 EventCacheService 的有效期設定
      await EventCacheService.updateCacheExpiry(Duration(days: days));

      if (mounted) {
        setState(() {
          _cacheExpiryDays = days;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('快取有效期已設定為 $days 天'),
            backgroundColor: Colors.green,
          ),
        );

        // 觸發設定變更回調
        widget.onSettingsChanged?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存設定失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 載入事件偵測設定
  Future<void> _loadEventDetectionSettings() async {
    try {
      // 載入最低事件分數
      final minimumScore = await UserPreferences.getEventMinimumScore();

      // 載入啟用的事件類型
      final enabledTypesJson = await UserPreferences.getEventEnabledTypes();
      Set<AstroEventType> enabledTypes = {};

      if (enabledTypesJson != null) {
        final enabledTypesList = jsonDecode(enabledTypesJson) as List;
        enabledTypes = enabledTypesList
            .map((index) => AstroEventType.values[index as int])
            .toSet();
      } else {
        // 預設啟用的事件類型
        enabledTypes = {
          AstroEventType.transitAspect,
          AstroEventType.progressionAspect,
        };
      }

      if (mounted) {
        setState(() {
          _minimumEventScore = minimumScore;
          _enabledEventTypes = enabledTypes;
        });
      }
    } catch (e) {
      logger.e('載入事件偵測設定失敗: $e');
    }
  }

  /// 保存事件偵測設定
  Future<void> _saveEventDetectionSettings() async {
    try {
      // 保存最低事件分數
      await UserPreferences.saveEventMinimumScore(_minimumEventScore);

      // 保存啟用的事件類型
      final enabledTypesIndices = _enabledEventTypes.map((type) => type.index).toList();
      await UserPreferences.saveEventEnabledTypes(jsonEncode(enabledTypesIndices));

      logger.d('事件偵測設定已保存');

      // 觸發設定變更回調
      widget.onSettingsChanged?.call();
    } catch (e) {
      logger.e('保存事件偵測設定失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存設定失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 切換事件類型
  Future<void> _toggleEventType(AstroEventType eventType, bool enabled) async {
    setState(() {
      if (enabled) {
        _enabledEventTypes.add(eventType);
      } else {
        _enabledEventTypes.remove(eventType);
      }
    });

    await _saveEventDetectionSettings();

    if (mounted) {
      final eventTypeName = _getEventTypeName(eventType);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${enabled ? "啟用" : "停用"} $eventTypeName'),
          backgroundColor: enabled ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  /// 獲取事件類型名稱
  String _getEventTypeName(AstroEventType eventType) {
    switch (eventType) {
      case AstroEventType.transitAspect:
        return '行運相位';
      case AstroEventType.progressionAspect:
        return '推運相位';
      case AstroEventType.solarArcAspect:
        return '太陽弧推運';
      case AstroEventType.planetSignChange:
        return '行星換座';
      case AstroEventType.planetHouseChange:
        return '行星換宮';
      default:
        return eventType.toString();
    }
  }

  /// 顯示最低分數設定對話框
  Future<void> _showMinimumScoreDialog() async {
    double selectedScore = _minimumEventScore;

    final result = await showDialog<double>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('設定最低事件分數'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('只顯示分數高於此閾值的事件：'),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('分數：'),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Slider(
                      value: selectedScore,
                      min: 0,
                      max: 100,
                      divisions: 20,
                      label: '${selectedScore.toInt()} 分',
                      onChanged: (value) {
                        setDialogState(() {
                          selectedScore = value;
                        });
                      },
                    ),
                  ),
                  Text('${selectedScore.toInt()} 分'),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                '較低的分數會顯示更多事件，但可能包含較不重要的事件。\n較高的分數只顯示重要事件，但可能遺漏一些有意義的事件。',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(selectedScore),
              child: const Text('確定'),
            ),
          ],
        ),
      ),
    );

    if (result != null && result != _minimumEventScore) {
      setState(() {
        _minimumEventScore = result;
      });

      await _saveEventDetectionSettings();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('最低事件分數已設定為 ${result.toInt()} 分'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// 顯示快取有效期設定對話框
  Future<void> _showCacheExpiryDialog() async {
    int selectedDays = _cacheExpiryDays;

    final result = await showDialog<int>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('設定快取有效期'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('選擇快取資料的有效期限：'),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('有效期：'),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Slider(
                      value: selectedDays.toDouble(),
                      min: 1,
                      max: 30,
                      divisions: 29,
                      label: '$selectedDays 天',
                      onChanged: (value) {
                        setDialogState(() {
                          selectedDays = value.round();
                        });
                      },
                    ),
                  ),
                  Text('$selectedDays 天'),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                '較短的有效期會確保資料更新，但會增加計算次數。\n較長的有效期可以提升效能，但資料可能不是最新的。',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(selectedDays),
              child: const Text('確定'),
            ),
          ],
        ),
      ),
    );

    if (result != null && result != _cacheExpiryDays) {
      await _saveCacheExpirySettings(result);
    }
  }
}
