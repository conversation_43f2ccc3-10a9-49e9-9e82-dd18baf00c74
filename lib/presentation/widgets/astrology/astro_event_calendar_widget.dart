import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../../../presentation/themes/app_theme.dart';

/// 事件日曆主題配色
class EventCalendarTheme {
  /// 背景顏色
  final Color backgroundColor;

  /// 標題區域顏色
  final Color headerColor;

  /// 標題文字顏色
  final Color headerTextColor;

  /// 一般文字顏色
  final Color textColor;

  /// 選中日期顏色
  final Color selectedDayColor;

  /// 今日顏色
  final Color todayColor;

  /// 標記顏色
  final Color markerColor;

  /// 邊框顏色
  final Color borderColor;

  /// 圖例背景顏色
  final Color legendBackgroundColor;

  /// 強度顏色漸層
  final List<Color> intensityColors;

  const EventCalendarTheme({
    required this.backgroundColor,
    required this.headerColor,
    required this.headerTextColor,
    required this.textColor,
    required this.selectedDayColor,
    required this.todayColor,
    required this.markerColor,
    required this.borderColor,
    required this.legendBackgroundColor,
    required this.intensityColors,
  });

  /// Starmaster 專業模式主題
  const EventCalendarTheme.starmaster()
      : backgroundColor = Colors.white,
        headerColor = AppColors.starmasterPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starmasterTextPrimary,
        selectedDayColor = AppColors.starmasterAccent,
        todayColor = AppColors.starmasterSecondary,
        markerColor = AppColors.starmasterAccent,
        borderColor = AppColors.starmasterBorder,
        legendBackgroundColor = AppColors.starmasterSurface,
        intensityColors = const [
          Color(0xFFE8F4FD), // 極淺藍
          Color(0xFFB3D9F7), // 淺藍
          Color(0xFF7FBEF0), // 中藍
          Color(0xFF4BA3E8), // 深藍
          Color(0xFF1E88E5), // 極深藍
        ];

  /// Starlight 初心者模式主題
  const EventCalendarTheme.starlight()
      : backgroundColor = AppColors.starlightBackground,
        headerColor = AppColors.starlightPrimary,
        headerTextColor = Colors.white,
        textColor = AppColors.starlightTextPrimary,
        selectedDayColor = AppColors.starlightAccent,
        todayColor = AppColors.starlightSecondary,
        markerColor = AppColors.starlightAccent,
        borderColor = AppColors.starlightBorder,
        legendBackgroundColor = AppColors.starlightSurface,
        intensityColors = const [
          Color(0xFFFFF8E1), // 極淺琥珀
          Color(0xFFFFE0B2), // 淺琥珀
          Color(0xFFFFCC80), // 中琥珀
          Color(0xFFFFB74D), // 深琥珀
          Color(0xFFF57C00), // 極深琥珀
        ];

  /// 深色模式主題
  const EventCalendarTheme.dark()
      : backgroundColor = const Color(0xFF1E1E1E),
        headerColor = const Color(0xFF2D2D2D),
        headerTextColor = Colors.white,
        textColor = Colors.white,
        selectedDayColor = const Color(0xFF6200EA),
        todayColor = const Color(0xFF03DAC6),
        markerColor = const Color(0xFF6200EA),
        borderColor = const Color(0xFF404040),
        legendBackgroundColor = const Color(0xFF2D2D2D),
        intensityColors = const [
          Color(0xFF2D2D2D), // 極暗
          Color(0xFF424242), // 暗
          Color(0xFF616161), // 中
          Color(0xFF9E9E9E), // 亮
          Color(0xFFE0E0E0), // 極亮
        ];

  /// 根據強度獲取顏色
  Color getIntensityColor(double intensity) {
    if (intensity <= 0) return intensityColors[0];
    if (intensity >= 1) return intensityColors.last;

    final index = (intensity * (intensityColors.length - 1)).floor();
    final nextIndex = (index + 1).clamp(0, intensityColors.length - 1);
    final factor = (intensity * (intensityColors.length - 1)) - index;

    return Color.lerp(intensityColors[index], intensityColors[nextIndex], factor) ?? intensityColors[index];
  }
}

/// 占星事件年曆熱度圖組件
/// 
/// 使用顏色強度顯示每日事件分數，支援多主題配色方案
class AstroEventCalendarWidget extends StatefulWidget {
  /// 事件時間軸資料
  final EventTimelineData timelineData;
  
  /// 點擊日期回調
  final Function(DateTime date, DailyEventScore? score)? onDayTapped;
  
  /// 主題配色方案
  final EventCalendarTheme theme;
  
  /// 是否顯示週數
  final bool showWeekNumbers;
  
  /// 是否顯示分數標籤
  final bool showScoreLabels;

  const AstroEventCalendarWidget({
    super.key,
    required this.timelineData,
    this.onDayTapped,
    this.theme = const EventCalendarTheme.starmaster(),
    this.showWeekNumbers = false,
    this.showScoreLabels = true,
  });

  @override
  State<AstroEventCalendarWidget> createState() => _AstroEventCalendarWidgetState();
}

class _AstroEventCalendarWidgetState extends State<AstroEventCalendarWidget> {
  late DateTime _focusedDay;
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _focusedDay = widget.timelineData.startDate;
    _selectedDay = null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.theme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildCalendar(),
          if (widget.showScoreLabels) _buildLegend(),
        ],
      ),
    );
  }

  /// 建立標題區域
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.theme.headerColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            color: widget.theme.headerTextColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            '占星事件年曆',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: widget.theme.headerTextColor,
            ),
          ),
          const Spacer(),
          Text(
            '${widget.timelineData.startDate.year}年',
            style: TextStyle(
              fontSize: 16,
              color: widget.theme.headerTextColor.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// 建立日曆主體
  Widget _buildCalendar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TableCalendar<DailyEventScore>(
        firstDay: widget.timelineData.startDate,
        lastDay: widget.timelineData.endDate,
        focusedDay: _focusedDay,
        selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
        eventLoader: _getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,
        calendarFormat: CalendarFormat.month,
        headerStyle: HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
          titleTextStyle: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: widget.theme.textColor,
          ),
          leftChevronIcon: Icon(
            Icons.chevron_left,
            color: widget.theme.textColor,
          ),
          rightChevronIcon: Icon(
            Icons.chevron_right,
            color: widget.theme.textColor,
          ),
        ),
        daysOfWeekStyle: DaysOfWeekStyle(
          weekdayStyle: TextStyle(
            color: widget.theme.textColor.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
          weekendStyle: TextStyle(
            color: widget.theme.textColor.withOpacity(0.5),
            fontWeight: FontWeight.w500,
          ),
        ),
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(
            color: widget.theme.textColor.withOpacity(0.7),
          ),
          defaultTextStyle: TextStyle(
            color: widget.theme.textColor,
          ),
          selectedDecoration: BoxDecoration(
            color: widget.theme.selectedDayColor,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: widget.theme.todayColor,
            shape: BoxShape.circle,
          ),
          markerDecoration: BoxDecoration(
            color: widget.theme.markerColor,
            shape: BoxShape.circle,
          ),
        ),
        calendarBuilders: CalendarBuilders<DailyEventScore>(
          defaultBuilder: _buildDayCell,
          selectedBuilder: _buildSelectedDayCell,
          todayBuilder: _buildTodayCell,
        ),
        onDaySelected: (selectedDay, focusedDay) {
          setState(() {
            _selectedDay = selectedDay;
            _focusedDay = focusedDay;
          });
          
          final score = widget.timelineData.getScoreForDate(selectedDay);
          widget.onDayTapped?.call(selectedDay, score);
        },
        onPageChanged: (focusedDay) {
          setState(() {
            _focusedDay = focusedDay;
          });
        },
      ),
    );
  }

  /// 建立圖例
  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.theme.legendBackgroundColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '事件強度圖例',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: widget.theme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: [
              _buildLegendItem('無事件', widget.theme.getIntensityColor(0)),
              _buildLegendItem('低強度', widget.theme.getIntensityColor(0.25)),
              _buildLegendItem('中強度', widget.theme.getIntensityColor(0.5)),
              _buildLegendItem('高強度', widget.theme.getIntensityColor(0.75)),
              _buildLegendItem('極高強度', widget.theme.getIntensityColor(1.0)),
            ],
          ),
        ],
      ),
    );
  }

  /// 建立圖例項目
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: widget.theme.textColor.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  /// 建立日期格子
  Widget? _buildDayCell(BuildContext context, DateTime day, DateTime focusedDay) {
    final score = widget.timelineData.getScoreForDate(day);
    final intensity = _calculateIntensity(score?.totalScore ?? 0);
    
    return Container(
      margin: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: widget.theme.getIntensityColor(intensity),
        shape: BoxShape.circle,
        border: Border.all(
          color: widget.theme.borderColor.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: _getTextColorForIntensity(intensity),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 建立選中日期格子
  Widget? _buildSelectedDayCell(BuildContext context, DateTime day, DateTime focusedDay) {
    final score = widget.timelineData.getScoreForDate(day);
    final intensity = _calculateIntensity(score?.totalScore ?? 0);
    
    return Container(
      margin: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: widget.theme.getIntensityColor(intensity),
        shape: BoxShape.circle,
        border: Border.all(
          color: widget.theme.selectedDayColor,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: widget.theme.selectedDayColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 建立今日格子
  Widget? _buildTodayCell(BuildContext context, DateTime day, DateTime focusedDay) {
    final score = widget.timelineData.getScoreForDate(day);
    final intensity = _calculateIntensity(score?.totalScore ?? 0);
    
    return Container(
      margin: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: widget.theme.getIntensityColor(intensity),
        shape: BoxShape.circle,
        border: Border.all(
          color: widget.theme.todayColor,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            color: widget.theme.todayColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 獲取指定日期的事件
  List<DailyEventScore> _getEventsForDay(DateTime day) {
    final score = widget.timelineData.getScoreForDate(day);
    return score != null ? [score] : [];
  }

  /// 計算強度值 (0-1)
  double _calculateIntensity(double score) {
    if (widget.timelineData.maxScore <= 0) return 0;
    return (score / widget.timelineData.maxScore).clamp(0.0, 1.0);
  }

  /// 根據強度獲取文字顏色
  Color _getTextColorForIntensity(double intensity) {
    return intensity > 0.5 ? Colors.white : widget.theme.textColor;
  }
}
