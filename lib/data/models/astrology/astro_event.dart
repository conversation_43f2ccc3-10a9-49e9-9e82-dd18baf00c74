import 'package:flutter/material.dart';

/// 星象事件類型枚舉
enum AstroEventType {
  moonPhase,        // 月相
  seasonChange,     // 節氣變化
  planetAspect,     // 行星相位
  planetSignChange, // 行星換座
  planetRetrograde, // 行星逆行
  eclipse,          // 日月蝕
  // 新增事件偵測相關類型
  transitAspect,    // 行運相位
  progressionAspect, // 推運相位
  solarArcAspect,   // 太陽弧推運相位
  planetHouseChange, // 行星換宮
  solarReturn,      // 太陽返照
  lunarReturn,      // 月亮返照
}

/// 月相類型枚舉
enum MoonPhaseType {
  newMoon,      // 新月
  waxingCrescent, // 娥眉月
  firstQuarter, // 上弦月
  waxingGibbous, // 盈凸月
  fullMoon,     // 滿月
  waningGibbous, // 虧凸月
  lastQuarter,  // 下弦月
  waningCrescent, // 殘月
}

/// 日月蝕類型枚舉
enum EclipseType {
  solarTotal,     // 日全蝕
  solarAnnular,   // 日環蝕
  solarPartial,   // 日偏蝕
  solarHybrid,    // 日混合蝕
  lunarTotal,     // 月全蝕
  lunarPartial,   // 月偏蝕
  lunarPenumbral, // 月半影蝕
}

/// 日食階段枚舉（對應Swiss Ephemeris times陣列）
enum SolarEclipsePhase {
  maximum,        // tret[0] 最大日食時間（食甚）
  localNoon,      // tret[1] 日食發生在當地正午的時間
  eclipseBegin,   // tret[2] 日食開始時間（初虧）
  eclipseEnd,     // tret[3] 日食結束時間（復圓）
  totalityBegin,  // tret[4] 全食開始時間（食既）
  totalityEnd,    // tret[5] 全食結束時間（生光）
  centerLineBegin,// tret[6] 中心線開始時間
  centerLineEnd,  // tret[7] 中心線結束時間
  annularToTotal, // tret[8] 日環食變成全食的時間
  totalToAnnular, // tret[9] 日全食再次變成日環食的時間
}

/// 月食階段枚舉
enum LunarEclipsePhase {
  penumbralBegin,   // 半影食開始
  partialBegin,     // 偏食開始
  totalBegin,       // 全食開始
  maximum,          // 食甚
  totalEnd,         // 全食結束
  partialEnd,       // 偏食結束
  penumbralEnd,     // 半影食結束
}

/// 可見性狀態枚舉
enum VisibilityStatus {
  visible,          // 可見
  notVisible,       // 不可見
  maybeVisible,     // 可能可見
  belowHorizon,     // 在地平線以下
  touchingHorizon,  // 觸及地平線
}

/// 日食詳細信息類別
class SolarEclipseDetails {
  final DateTime maximumTime;           // 食甚時間
  final DateTime? localNoonTime;        // 當地正午時間
  final DateTime? eclipseBeginTime;     // 初虧時間
  final DateTime? eclipseEndTime;       // 復圓時間
  final DateTime? totalityBeginTime;    // 食既時間
  final DateTime? totalityEndTime;      // 生光時間
  final DateTime? centerLineBeginTime;  // 中心線開始時間
  final DateTime? centerLineEndTime;    // 中心線結束時間
  final DateTime? annularToTotalTime;   // 環食變全食時間
  final DateTime? totalToAnnularTime;   // 全食變環食時間

  final EclipseType eclipseType;        // 日食類型
  final double magnitude;               // 食分
  final bool isVisible;                 // 可見性
  final Duration? totalDuration;        // 總持續時間
  final Duration? totalityDuration;     // 全食持續時間
  final String description;             // 描述

  SolarEclipseDetails({
    required this.maximumTime,
    this.localNoonTime,
    this.eclipseBeginTime,
    this.eclipseEndTime,
    this.totalityBeginTime,
    this.totalityEndTime,
    this.centerLineBeginTime,
    this.centerLineEndTime,
    this.annularToTotalTime,
    this.totalToAnnularTime,
    required this.eclipseType,
    required this.magnitude,
    required this.isVisible,
    this.totalDuration,
    this.totalityDuration,
    required this.description,
  });

  /// 獲取所有可用的時間階段
  Map<SolarEclipsePhase, DateTime?> getAllPhases() {
    return {
      SolarEclipsePhase.maximum: maximumTime,
      SolarEclipsePhase.localNoon: localNoonTime,
      SolarEclipsePhase.eclipseBegin: eclipseBeginTime,
      SolarEclipsePhase.eclipseEnd: eclipseEndTime,
      SolarEclipsePhase.totalityBegin: totalityBeginTime,
      SolarEclipsePhase.totalityEnd: totalityEndTime,
      SolarEclipsePhase.centerLineBegin: centerLineBeginTime,
      SolarEclipsePhase.centerLineEnd: centerLineEndTime,
      SolarEclipsePhase.annularToTotal: annularToTotalTime,
      SolarEclipsePhase.totalToAnnular: totalToAnnularTime,
    };
  }

  /// 獲取階段的中文名稱
  static String getPhaseName(SolarEclipsePhase phase) {
    switch (phase) {
      case SolarEclipsePhase.maximum:
        return '食甚';
      case SolarEclipsePhase.localNoon:
        return '當地正午';
      case SolarEclipsePhase.eclipseBegin:
        return '初虧';
      case SolarEclipsePhase.eclipseEnd:
        return '復圓';
      case SolarEclipsePhase.totalityBegin:
        return '食既';
      case SolarEclipsePhase.totalityEnd:
        return '生光';
      case SolarEclipsePhase.centerLineBegin:
        return '中心線開始';
      case SolarEclipsePhase.centerLineEnd:
        return '中心線結束';
      case SolarEclipsePhase.annularToTotal:
        return '環食變全食';
      case SolarEclipsePhase.totalToAnnular:
        return '全食變環食';
    }
  }
}

/// 月食詳細信息類別
class LunarEclipseDetails {
  final DateTime maximumTime;              // 食甚時間
  final DateTime? penumbralBeginTime;      // 半影食開始時間
  final DateTime? partialBeginTime;        // 偏食開始時間
  final DateTime? totalBeginTime;          // 全食開始時間
  final DateTime? totalEndTime;            // 全食結束時間
  final DateTime? partialEndTime;          // 偏食結束時間
  final DateTime? penumbralEndTime;        // 半影食結束時間

  final EclipseType eclipseType;           // 月食類型
  final double magnitude;                  // 食分
  final double penumbralMagnitude;         // 半影食分
  final bool isVisible;                    // 可見性
  final Duration? totalDuration;           // 總持續時間
  final Duration? totalityDuration;        // 全食持續時間
  final Duration? partialDuration;         // 偏食持續時間
  final Duration? penumbralDuration;       // 半影食持續時間
  final String description;                // 描述

  LunarEclipseDetails({
    required this.maximumTime,
    this.penumbralBeginTime,
    this.partialBeginTime,
    this.totalBeginTime,
    this.totalEndTime,
    this.partialEndTime,
    this.penumbralEndTime,
    required this.eclipseType,
    required this.magnitude,
    this.penumbralMagnitude = 0.0,
    required this.isVisible,
    this.totalDuration,
    this.totalityDuration,
    this.partialDuration,
    this.penumbralDuration,
    required this.description,
  });

  /// 獲取所有可用的時間階段
  Map<LunarEclipsePhase, DateTime?> getAllPhases() {
    return {
      LunarEclipsePhase.penumbralBegin: penumbralBeginTime,
      LunarEclipsePhase.partialBegin: partialBeginTime,
      LunarEclipsePhase.totalBegin: totalBeginTime,
      LunarEclipsePhase.maximum: maximumTime,
      LunarEclipsePhase.totalEnd: totalEndTime,
      LunarEclipsePhase.partialEnd: partialEndTime,
      LunarEclipsePhase.penumbralEnd: penumbralEndTime,
    };
  }

  /// 獲取月食階段的中文名稱
  static String getLunarPhaseName(LunarEclipsePhase phase) {
    switch (phase) {
      case LunarEclipsePhase.penumbralBegin:
        return '半影食開始';
      case LunarEclipsePhase.partialBegin:
        return '偏食開始';
      case LunarEclipsePhase.totalBegin:
        return '全食開始';
      case LunarEclipsePhase.maximum:
        return '食甚';
      case LunarEclipsePhase.totalEnd:
        return '全食結束';
      case LunarEclipsePhase.partialEnd:
        return '偏食結束';
      case LunarEclipsePhase.penumbralEnd:
        return '半影食結束';
    }
  }
}

/// 日食時間線條目
class EclipseTimelineEntry {
  final String phaseName;                 // 階段名稱
  final DateTime utcTime;                 // UTC時間
  final DateTime localTime;               // 當地時間
  final VisibilityStatus visibility;      // 可見性狀態
  final String? visibilityNote;           // 可見性說明

  EclipseTimelineEntry({
    required this.phaseName,
    required this.utcTime,
    required this.localTime,
    required this.visibility,
    this.visibilityNote,
  });

  /// 獲取可見性顯示文字
  String getVisibilityText() {
    switch (visibility) {
      case VisibilityStatus.visible:
        return '是的';
      case VisibilityStatus.notVisible:
        return '不，在地平線以下';
      case VisibilityStatus.maybeVisible:
        return '也許，觸及地平線';
      case VisibilityStatus.belowHorizon:
        return '不，在地平線以下';
      case VisibilityStatus.touchingHorizon:
        return '也許，觸及地平線';
    }
  }
}

/// 日食統計數據
class EclipseStatistics {
  final double magnitude;                  // 食分
  final double obscuration;                // 遮蔽百分比
  final double penumbralMagnitude;         // 半影食分（月食用）
  final Duration totalDuration;            // 總持續時間
  final Duration? totalityDuration;        // 全食持續時間
  final Duration? partialDuration;         // 偏食持續時間
  final Duration? penumbralDuration;       // 半影食持續時間

  EclipseStatistics({
    required this.magnitude,
    required this.obscuration,
    this.penumbralMagnitude = 0.0,
    required this.totalDuration,
    this.totalityDuration,
    this.partialDuration,
    this.penumbralDuration,
  });
}

/// 事件重要性等級
enum EventImportance {
  /// 極高重要性 (90-100分)
  critical,
  /// 高重要性 (70-89分)
  high,
  /// 中等重要性 (50-69分)
  medium,
  /// 低重要性 (30-49分)
  low,
  /// 極低重要性 (0-29分)
  minimal,
}

/// 星象事件模型
class AstroEvent {
  final String id;
  final String title;
  final String description;
  final DateTime dateTime;
  final AstroEventType type;
  final Color color;
  final IconData icon;
  final int importance; // 重要度 1-5 (保持向後相容)
  final bool isVisible; // 可見性
  final Map<String, dynamic>? additionalData;

  // 新增事件偵測相關欄位
  final double? score; // 事件分數 (0-100)
  final EventImportance? eventImportance; // 事件重要性等級
  final List<String>? involvedPlanets; // 涉及的行星
  final List<int>? involvedHouses; // 涉及的宮位
  final List<String>? involvedSigns; // 涉及的星座
  final String? aspectType; // 相位類型
  final double? orb; // 容許度
  final bool? isExact; // 是否為精確相位

  const AstroEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.dateTime,
    required this.type,
    required this.color,
    required this.icon,
    this.importance = 1,
    this.isVisible = true, // 預設為可見
    this.additionalData,
    // 新增欄位
    this.score,
    this.eventImportance,
    this.involvedPlanets,
    this.involvedHouses,
    this.involvedSigns,
    this.aspectType,
    this.orb,
    this.isExact,
  });

  /// 從 Map 創建 AstroEvent
  factory AstroEvent.fromMap(Map<String, dynamic> map) {
    return AstroEvent(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      dateTime: DateTime.parse(map['dateTime'] as String),
      type: AstroEventType.values[map['type'] as int],
      color: Color(map['color'] as int),
      icon: _getIconFromCodePoint(map['icon'] as int),
      importance: map['importance'] as int? ?? 1,
      isVisible: map['isVisible'] as bool? ?? true,
      additionalData: map['additionalData'] != null
          ? Map<String, dynamic>.from(map['additionalData'] as Map)
          : null,
      score: (map['score'] as num?)?.toDouble(),
      eventImportance: map['eventImportance'] != null
          ? EventImportance.values[map['eventImportance'] as int]
          : null,
      involvedPlanets: map['involvedPlanets'] != null
          ? List<String>.from(map['involvedPlanets'] as List)
          : null,
      involvedHouses: map['involvedHouses'] != null
          ? List<int>.from(map['involvedHouses'] as List)
          : null,
      involvedSigns: map['involvedSigns'] != null
          ? List<String>.from(map['involvedSigns'] as List)
          : null,
      aspectType: map['aspectType'] as String?,
      orb: (map['orb'] as num?)?.toDouble(),
      isExact: map['isExact'] as bool?,
    );
  }

  /// 從代碼點獲取圖標（使用常數圖標避免 tree shaking 問題）
  static IconData _getIconFromCodePoint(int codePoint) {
    // 使用預定義的常數圖標映射
    switch (codePoint) {
      case 0xe3a7: // Icons.wb_sunny
        return Icons.wb_sunny;
      case 0xe3a8: // Icons.brightness_3
        return Icons.brightness_3;
      case 0xe8d2: // Icons.sync
        return Icons.sync;
      case 0xe8e5: // Icons.trending_up
        return Icons.trending_up;
      case 0xe88a: // Icons.home
        return Icons.home;
      case 0xe8b5: // Icons.swap_horiz
        return Icons.swap_horiz;
      case 0xe8d4: // Icons.autorenew
        return Icons.autorenew;
      case 0xe8d5: // Icons.cached
        return Icons.cached;
      case 0xe8d6: // Icons.update
        return Icons.update;
      case 0xe8d7: // Icons.refresh
        return Icons.refresh;
      case 0xe8d8: // Icons.loop
        return Icons.loop;
      case 0xe8d9: // Icons.repeat
        return Icons.repeat;
      case 0xe8da: // Icons.replay
        return Icons.replay;
      case 0xe8db: // Icons.restore
        return Icons.restore;
      case 0xe8dc: // Icons.undo
        return Icons.undo;
      case 0xe8dd: // Icons.redo
        return Icons.redo;
      case 0xe8de: // Icons.rotate_left
        return Icons.rotate_left;
      case 0xe8df: // Icons.rotate_right
        return Icons.rotate_right;
      case 0xe8e0: // Icons.flip
        return Icons.flip;
      case 0xe8e1: // Icons.flip_camera_android
        return Icons.flip_camera_android;
      case 0xe8e2: // Icons.flip_camera_ios
        return Icons.flip_camera_ios;
      case 0xe8e3: // Icons.transform
        return Icons.transform;
      case 0xe8e4: // Icons.compare_arrows
        return Icons.compare_arrows;
      case 0xe8e6: // Icons.timeline
        return Icons.timeline;
      case 0xe8e7: // Icons.show_chart
        return Icons.show_chart;
      case 0xe8e8: // Icons.multiline_chart
        return Icons.multiline_chart;
      case 0xe8e9: // Icons.pie_chart
        return Icons.pie_chart;
      case 0xe8ea: // Icons.donut_large
        return Icons.donut_large;
      case 0xe8eb: // Icons.donut_small
        return Icons.donut_small;
      case 0xe8ec: // Icons.bubble_chart
        return Icons.bubble_chart;
      case 0xe8ed: // Icons.scatter_plot
        return Icons.scatter_plot;
      case 0xe8ee: // Icons.bar_chart
        return Icons.bar_chart;
      case 0xe8ef: // Icons.insert_chart
        return Icons.insert_chart;
      case 0xe8f0: // Icons.insert_chart_outlined
        return Icons.insert_chart_outlined;
      case 0xe8f1: // Icons.analytics
        return Icons.analytics;
      case 0xe8f2: // Icons.assessment
        return Icons.assessment;
      case 0xe8f3: // Icons.equalizer
        return Icons.equalizer;
      case 0xe8f4: // Icons.graphic_eq
        return Icons.graphic_eq;
      case 0xe8f5: // Icons.leaderboard
        return Icons.leaderboard;
      case 0xe8f6: // Icons.poll
        return Icons.poll;
      case 0xe8f7: // Icons.query_stats
        return Icons.query_stats;
      case 0xe8f8: // Icons.stacked_bar_chart
        return Icons.stacked_bar_chart;
      case 0xe8f9: // Icons.stacked_line_chart
        return Icons.stacked_line_chart;
      case 0xe8fa: // Icons.waterfall_chart
        return Icons.waterfall_chart;
      default:
        // 預設圖標
        return Icons.star;
    }
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'dateTime': dateTime.toIso8601String(),
      'type': type.index,
      'color': color.value,
      'icon': icon.codePoint,
      'importance': importance,
      'isVisible': isVisible,
      'additionalData': additionalData,
      'score': score,
      'eventImportance': eventImportance?.index,
      'involvedPlanets': involvedPlanets,
      'involvedHouses': involvedHouses,
      'involvedSigns': involvedSigns,
      'aspectType': aspectType,
      'orb': orb,
      'isExact': isExact,
    };
  }

  /// 獲取事件類型的顯示名稱
  String get typeDisplayName {
    switch (type) {
      case AstroEventType.moonPhase:
        return '月相';
      case AstroEventType.seasonChange:
        return '節氣';
      case AstroEventType.planetAspect:
        return '相位';
      case AstroEventType.planetSignChange:
        return '換座';
      case AstroEventType.planetRetrograde:
        return '逆行';
      case AstroEventType.eclipse:
        return '蝕相';
      case AstroEventType.transitAspect:
        return '行運相位';
      case AstroEventType.progressionAspect:
        return '推運相位';
      case AstroEventType.solarArcAspect:
        return '太陽弧推運';
      case AstroEventType.planetHouseChange:
        return '行星換宮';
      case AstroEventType.solarReturn:
        return '太陽返照';
      case AstroEventType.lunarReturn:
        return '月亮返照';
    }
  }

  /// 獲取重要度顏色
  Color get importanceColor {
    switch (importance) {
      case 5:
        return Colors.red;
      case 4:
        return Colors.orange;
      case 3:
        return Colors.amber;
      case 2:
        return Colors.blue;
      case 1:
      default:
        return Colors.grey;
    }
  }

  /// 獲取月相類型的顯示名稱
  static String getMoonPhaseDisplayName(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return '新月';
      case MoonPhaseType.waxingCrescent:
        return '娥眉月';
      case MoonPhaseType.firstQuarter:
        return '上弦月';
      case MoonPhaseType.waxingGibbous:
        return '盈凸月';
      case MoonPhaseType.fullMoon:
        return '滿月';
      case MoonPhaseType.waningGibbous:
        return '虧凸月';
      case MoonPhaseType.lastQuarter:
        return '下弦月';
      case MoonPhaseType.waningCrescent:
        return '殘月';
    }
  }

  /// 獲取月相圖標
  static IconData getMoonPhaseIcon(MoonPhaseType phase) {
    switch (phase) {
      case MoonPhaseType.newMoon:
        return Icons.brightness_1;
      case MoonPhaseType.waxingCrescent:
        return Icons.brightness_2;
      case MoonPhaseType.firstQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waxingGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.fullMoon:
        return Icons.brightness_7;
      case MoonPhaseType.waningGibbous:
        return Icons.brightness_4;
      case MoonPhaseType.lastQuarter:
        return Icons.brightness_3;
      case MoonPhaseType.waningCrescent:
        return Icons.brightness_2;
    }
  }

  /// 獲取日月蝕顯示名稱
  static String getEclipseDisplayName(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return '日全蝕';
      case EclipseType.solarAnnular:
        return '日環蝕';
      case EclipseType.solarPartial:
        return '日偏蝕';
      case EclipseType.solarHybrid:
        return '日混合蝕';
      case EclipseType.lunarTotal:
        return '月全蝕';
      case EclipseType.lunarPartial:
        return '月偏蝕';
      case EclipseType.lunarPenumbral:
        return '月半影蝕';
    }
  }

  /// 獲取日月蝕圖標
  static IconData getEclipseIcon(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
      case EclipseType.solarAnnular:
      case EclipseType.solarPartial:
      case EclipseType.solarHybrid:
        return Icons.wb_sunny_outlined; // 日蝕圖標
      case EclipseType.lunarTotal:
      case EclipseType.lunarPartial:
      case EclipseType.lunarPenumbral:
        return Icons.nightlight_round; // 月蝕圖標
    }
  }

  /// 獲取日月蝕顏色
  static Color getEclipseColor(EclipseType eclipseType) {
    switch (eclipseType) {
      case EclipseType.solarTotal:
        return Colors.black87; // 日全蝕 - 黑色
      case EclipseType.solarAnnular:
        return Colors.orange; // 日環蝕 - 橙色
      case EclipseType.solarPartial:
        return Colors.amber; // 日偏蝕 - 琥珀色
      case EclipseType.solarHybrid:
        return Colors.deepOrange; // 日混合蝕 - 深橙色
      case EclipseType.lunarTotal:
        return Colors.red.shade800; // 月全蝕 - 深紅色
      case EclipseType.lunarPartial:
        return Colors.red.shade400; // 月偏蝕 - 紅色
      case EclipseType.lunarPenumbral:
        return Colors.grey.shade600; // 月半影蝕 - 灰色
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AstroEvent &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AstroEvent{id: $id, title: $title, dateTime: $dateTime, type: $type, isVisible: $isVisible}';
  }
}

/// 事件重要性擴展方法
extension EventImportanceExtension on EventImportance {
  /// 獲取重要性的顯示名稱
  String get displayName {
    switch (this) {
      case EventImportance.critical:
        return '極重要';
      case EventImportance.high:
        return '重要';
      case EventImportance.medium:
        return '中等';
      case EventImportance.low:
        return '輕微';
      case EventImportance.minimal:
        return '微弱';
    }
  }

  /// 獲取重要性的顏色
  Color get color {
    switch (this) {
      case EventImportance.critical:
        return Colors.red;
      case EventImportance.high:
        return Colors.orange;
      case EventImportance.medium:
        return Colors.yellow;
      case EventImportance.low:
        return Colors.lightGreen;
      case EventImportance.minimal:
        return Colors.grey;
    }
  }

  /// 從分數獲取重要性等級
  static EventImportance fromScore(double score) {
    if (score >= 90) return EventImportance.critical;
    if (score >= 70) return EventImportance.high;
    if (score >= 50) return EventImportance.medium;
    if (score >= 30) return EventImportance.low;
    return EventImportance.minimal;
  }
}

/// 事件類型擴展方法
extension AstroEventTypeExtension on AstroEventType {
  /// 獲取事件類型的圖標
  IconData get icon {
    switch (this) {
      case AstroEventType.moonPhase:
        return Icons.brightness_2;
      case AstroEventType.seasonChange:
        return Icons.nature;
      case AstroEventType.planetAspect:
        return Icons.sync;
      case AstroEventType.planetSignChange:
        return Icons.swap_horiz;
      case AstroEventType.planetRetrograde:
        return Icons.replay;
      case AstroEventType.eclipse:
        return Icons.brightness_1;
      case AstroEventType.transitAspect:
        return Icons.sync;
      case AstroEventType.progressionAspect:
        return Icons.trending_up;
      case AstroEventType.solarArcAspect:
        return Icons.wb_sunny;
      case AstroEventType.planetHouseChange:
        return Icons.home;
      case AstroEventType.solarReturn:
        return Icons.wb_sunny;
      case AstroEventType.lunarReturn:
        return Icons.brightness_3;
    }
  }

  /// 獲取事件類型的預設顏色
  Color get defaultColor {
    switch (this) {
      case AstroEventType.moonPhase:
        return Colors.indigo;
      case AstroEventType.seasonChange:
        return Colors.teal;
      case AstroEventType.planetAspect:
        return Colors.blue;
      case AstroEventType.planetSignChange:
        return Colors.purple;
      case AstroEventType.planetRetrograde:
        return Colors.red;
      case AstroEventType.eclipse:
        return Colors.deepPurple;
      case AstroEventType.transitAspect:
        return Colors.blue;
      case AstroEventType.progressionAspect:
        return Colors.green;
      case AstroEventType.solarArcAspect:
        return Colors.orange;
      case AstroEventType.planetHouseChange:
        return Colors.brown;
      case AstroEventType.solarReturn:
        return Colors.amber;
      case AstroEventType.lunarReturn:
        return Colors.cyan;
    }
  }
}
