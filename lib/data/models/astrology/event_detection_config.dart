import 'astro_event.dart';

/// 事件偵測配置模型
class EventDetectionConfig {
  /// 要偵測的事件類型
  final Set<AstroEventType> enabledEventTypes;
  
  /// 偵測時間範圍（天數）
  final int detectionRangeDays;
  
  /// 最小事件分數閾值
  final double minimumEventScore;
  
  /// 是否啟用個人化分析
  final bool enablePersonalization;
  
  /// 推運技法設定
  final ProgressionSettings progressionSettings;
  
  /// 相位設定
  final AspectSettings aspectSettings;
  
  /// 行星設定
  final PlanetSettings planetSettings;
  
  /// 宮位設定
  final HouseSettings houseSettings;

  const EventDetectionConfig({
    required this.enabledEventTypes,
    this.detectionRangeDays = 365,
    this.minimumEventScore = 20.0,
    this.enablePersonalization = true,
    required this.progressionSettings,
    required this.aspectSettings,
    required this.planetSettings,
    required this.houseSettings,
  });

  /// 預設配置
  factory EventDetectionConfig.defaultConfig() {
    return EventDetectionConfig(
      enabledEventTypes: {
        AstroEventType.transitAspect,
        AstroEventType.progressionAspect,
        AstroEventType.solarArcAspect,
        AstroEventType.planetSignChange,
        AstroEventType.planetHouseChange,
        AstroEventType.moonPhase,
        AstroEventType.planetRetrograde,
        AstroEventType.solarReturn,
        AstroEventType.lunarReturn,
      },
      progressionSettings: ProgressionSettings.defaultSettings(),
      aspectSettings: AspectSettings.defaultSettings(),
      planetSettings: PlanetSettings.defaultSettings(),
      houseSettings: HouseSettings.defaultSettings(),
    );
  }

  /// 從 Map 創建配置
  factory EventDetectionConfig.fromMap(Map<String, dynamic> map) {
    return EventDetectionConfig(
      enabledEventTypes: (map['enabledEventTypes'] as List)
          .map((e) => AstroEventType.values[e as int])
          .toSet(),
      detectionRangeDays: map['detectionRangeDays'] as int? ?? 365,
      minimumEventScore: (map['minimumEventScore'] as num?)?.toDouble() ?? 20.0,
      enablePersonalization: map['enablePersonalization'] as bool? ?? true,
      progressionSettings: ProgressionSettings.fromMap(
          map['progressionSettings'] as Map<String, dynamic>),
      aspectSettings: AspectSettings.fromMap(
          map['aspectSettings'] as Map<String, dynamic>),
      planetSettings: PlanetSettings.fromMap(
          map['planetSettings'] as Map<String, dynamic>),
      houseSettings: HouseSettings.fromMap(
          map['houseSettings'] as Map<String, dynamic>),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'enabledEventTypes': enabledEventTypes.map((e) => e.index).toList(),
      'detectionRangeDays': detectionRangeDays,
      'minimumEventScore': minimumEventScore,
      'enablePersonalization': enablePersonalization,
      'progressionSettings': progressionSettings.toMap(),
      'aspectSettings': aspectSettings.toMap(),
      'planetSettings': planetSettings.toMap(),
      'houseSettings': houseSettings.toMap(),
    };
  }

  /// 複製並修改
  EventDetectionConfig copyWith({
    Set<AstroEventType>? enabledEventTypes,
    int? detectionRangeDays,
    double? minimumEventScore,
    bool? enablePersonalization,
    ProgressionSettings? progressionSettings,
    AspectSettings? aspectSettings,
    PlanetSettings? planetSettings,
    HouseSettings? houseSettings,
  }) {
    return EventDetectionConfig(
      enabledEventTypes: enabledEventTypes ?? this.enabledEventTypes,
      detectionRangeDays: detectionRangeDays ?? this.detectionRangeDays,
      minimumEventScore: minimumEventScore ?? this.minimumEventScore,
      enablePersonalization: enablePersonalization ?? this.enablePersonalization,
      progressionSettings: progressionSettings ?? this.progressionSettings,
      aspectSettings: aspectSettings ?? this.aspectSettings,
      planetSettings: planetSettings ?? this.planetSettings,
      houseSettings: houseSettings ?? this.houseSettings,
    );
  }
}

/// 推運設定
class ProgressionSettings {
  /// 啟用的推運技法
  final Set<ProgressionType> enabledProgressions;
  
  /// 推運容許度設定
  final Map<ProgressionType, double> progressionOrbs;
  
  /// 推運速度設定
  final Map<ProgressionType, double> progressionSpeeds;

  const ProgressionSettings({
    required this.enabledProgressions,
    required this.progressionOrbs,
    required this.progressionSpeeds,
  });

  /// 預設設定
  factory ProgressionSettings.defaultSettings() {
    return ProgressionSettings(
      enabledProgressions: {
        ProgressionType.transit,
        ProgressionType.secondary,
        ProgressionType.solarArc,
      },
      progressionOrbs: {
        ProgressionType.transit: 2.0,
        ProgressionType.secondary: 1.0,
        ProgressionType.solarArc: 1.0,
        ProgressionType.tertiary: 0.5,
      },
      progressionSpeeds: {
        ProgressionType.secondary: 1.0, // 一天等於一年
        ProgressionType.tertiary: 1.0/12, // 一天等於一月
        ProgressionType.solarArc: 1.0, // 太陽弧度
      },
    );
  }

  /// 從 Map 創建設定
  factory ProgressionSettings.fromMap(Map<String, dynamic> map) {
    return ProgressionSettings(
      enabledProgressions: (map['enabledProgressions'] as List)
          .map((e) => ProgressionType.values[e as int])
          .toSet(),
      progressionOrbs: (map['progressionOrbs'] as Map).map(
          (k, v) => MapEntry(ProgressionType.values[int.parse(k)], v.toDouble())),
      progressionSpeeds: (map['progressionSpeeds'] as Map).map(
          (k, v) => MapEntry(ProgressionType.values[int.parse(k)], v.toDouble())),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'enabledProgressions': enabledProgressions.map((e) => e.index).toList(),
      'progressionOrbs': progressionOrbs.map((k, v) => MapEntry(k.index.toString(), v)),
      'progressionSpeeds': progressionSpeeds.map((k, v) => MapEntry(k.index.toString(), v)),
    };
  }
}

/// 推運類型枚舉
enum ProgressionType {
  transit,    // 行運
  secondary,  // 次限推運
  tertiary,   // 三限推運
  solarArc,   // 太陽弧推運
}

/// 相位設定
class AspectSettings {
  /// 啟用的相位類型
  final Set<String> enabledAspects;
  
  /// 相位容許度設定
  final Map<String, double> aspectOrbs;
  
  /// 是否包含次要相位
  final bool includeMinorAspects;

  const AspectSettings({
    required this.enabledAspects,
    required this.aspectOrbs,
    this.includeMinorAspects = false,
  });

  /// 預設設定
  factory AspectSettings.defaultSettings() {
    return AspectSettings(
      enabledAspects: {
        '合相', '對沖', '四分相', '三分相', '六分相'
      },
      aspectOrbs: {
        '合相': 8.0,
        '對沖': 8.0,
        '四分相': 6.0,
        '三分相': 6.0,
        '六分相': 4.0,
        '半四分相': 2.0,
        '半三分相': 2.0,
        '五分相': 2.0,
      },
    );
  }

  /// 從 Map 創建設定
  factory AspectSettings.fromMap(Map<String, dynamic> map) {
    return AspectSettings(
      enabledAspects: Set<String>.from(map['enabledAspects'] as List),
      aspectOrbs: Map<String, double>.from(map['aspectOrbs'] as Map),
      includeMinorAspects: map['includeMinorAspects'] as bool? ?? false,
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'enabledAspects': enabledAspects.toList(),
      'aspectOrbs': aspectOrbs,
      'includeMinorAspects': includeMinorAspects,
    };
  }
}

/// 行星設定
class PlanetSettings {
  /// 啟用的行星
  final Set<String> enabledPlanets;
  
  /// 行星權重設定
  final Map<String, double> planetWeights;

  const PlanetSettings({
    required this.enabledPlanets,
    required this.planetWeights,
  });

  /// 預設設定
  factory PlanetSettings.defaultSettings() {
    return PlanetSettings(
      enabledPlanets: {
        '太陽', '月亮', '水星', '金星', '火星', 
        '木星', '土星', '天王星', '海王星', '冥王星'
      },
      planetWeights: {
        '太陽': 1.0,
        '月亮': 1.0,
        '水星': 0.8,
        '金星': 0.8,
        '火星': 0.8,
        '木星': 0.6,
        '土星': 0.6,
        '天王星': 0.4,
        '海王星': 0.4,
        '冥王星': 0.4,
      },
    );
  }

  /// 從 Map 創建設定
  factory PlanetSettings.fromMap(Map<String, dynamic> map) {
    return PlanetSettings(
      enabledPlanets: Set<String>.from(map['enabledPlanets'] as List),
      planetWeights: Map<String, double>.from(map['planetWeights'] as Map),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'enabledPlanets': enabledPlanets.toList(),
      'planetWeights': planetWeights,
    };
  }
}

/// 宮位設定
class HouseSettings {
  /// 啟用的宮位
  final Set<int> enabledHouses;
  
  /// 宮位重要性權重
  final Map<int, double> houseWeights;

  const HouseSettings({
    required this.enabledHouses,
    required this.houseWeights,
  });

  /// 預設設定
  factory HouseSettings.defaultSettings() {
    return HouseSettings(
      enabledHouses: {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12},
      houseWeights: {
        1: 1.0,  // 第一宮 - 始宮
        4: 1.0,  // 第四宮 - 始宮
        7: 1.0,  // 第七宮 - 始宮
        10: 1.0, // 第十宮 - 始宮
        2: 0.8,  // 第二宮 - 續宮
        5: 0.8,  // 第五宮 - 續宮
        8: 0.8,  // 第八宮 - 續宮
        11: 0.8, // 第十一宮 - 續宮
        3: 0.6,  // 第三宮 - 果宮
        6: 0.6,  // 第六宮 - 果宮
        9: 0.6,  // 第九宮 - 果宮
        12: 0.6, // 第十二宮 - 果宮
      },
    );
  }

  /// 從 Map 創建設定
  factory HouseSettings.fromMap(Map<String, dynamic> map) {
    return HouseSettings(
      enabledHouses: Set<int>.from(map['enabledHouses'] as List),
      houseWeights: Map<int, double>.from(
        (map['houseWeights'] as Map).map((k, v) => MapEntry(int.parse(k.toString()), v.toDouble()))
      ),
    );
  }

  /// 轉換為 Map
  Map<String, dynamic> toMap() {
    return {
      'enabledHouses': enabledHouses.toList(),
      'houseWeights': houseWeights.map((k, v) => MapEntry(k.toString(), v)),
    };
  }
}
