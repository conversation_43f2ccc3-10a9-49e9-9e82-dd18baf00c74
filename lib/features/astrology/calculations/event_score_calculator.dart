import 'dart:math' as math;

import '../../../data/models/astrology/aspect_info.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/planet_position.dart';
import '../../../data/models/user/birth_data.dart';
import '../constants/aspect_definitions.dart';

/// 事件評分計算器
/// 
/// 負責計算占星事件的評分，結合多種因素：
/// - 行星重要性權重
/// - 相位強度
/// - 宮位影響
/// - 時間精確度
/// - 個人化影響
class EventScoreCalculator {
  /// 事件評分配置
  final EventScoreConfig config;

  const EventScoreCalculator({
    required this.config,
  });

  /// 計算相位事件的評分
  /// 
  /// [aspect] 相位資訊
  /// [birthData] 出生資料（用於個人化計算）
  /// [isTransit] 是否為行運相位
  /// [isPrecise] 是否為精確相位
  EventScore calculateAspectScore(
    AspectInfo aspect,
    BirthData birthData, {
    bool isTransit = true,
    bool isPrecise = false,
  }) {
    // 1. 計算行星權重
    final planetWeight = _calculatePlanetWeight(
      aspect.planet1.name,
      aspect.planet2.name,
    );

    // 2. 計算相位強度
    final aspectStrength = _calculateAspectStrength(
      aspect.aspect,
      aspect.orb,
    );

    // 3. 計算宮位重要性
    final houseImportance = _calculateHouseImportance(
      aspect.planet1.house,
      aspect.planet2.house,
    );

    // 4. 計算時間精確度
    final timeAccuracy = _calculateTimeAccuracy(
      aspect.orb,
      AspectDefinitions.getDefaultOrb(aspect.aspect),
    );

    // 5. 計算個人化影響
    final personalImpact = _calculatePersonalImpact(
      aspect,
      birthData,
      isTransit,
    );

    // 6. 計算總分數
    final totalScore = _calculateTotalScore(
      planetWeight,
      aspectStrength,
      houseImportance,
      timeAccuracy,
      personalImpact,
      isPrecise,
    );

    // 7. 生成評分說明
    final explanation = _generateExplanation(
      aspect,
      planetWeight,
      aspectStrength,
      houseImportance,
      timeAccuracy,
      personalImpact,
      totalScore,
    );

    return EventScore(
      totalScore: totalScore,
      planetWeight: planetWeight,
      aspectStrength: aspectStrength,
      houseImportance: houseImportance,
      timeAccuracy: timeAccuracy,
      personalImpact: personalImpact,
      explanation: explanation,
      calculatedAt: DateTime.now(),
    );
  }

  /// 計算行星換座事件的評分
  EventScore calculateSignChangeScore(
    String planetName,
    String fromSign,
    String toSign,
    BirthData birthData,
  ) {
    // 行星換座的基礎分數
    final planetWeight = config.planetWeights[planetName] ?? 0.5;
    final baseScore = planetWeight * 30; // 基礎分數

    // 星座重要性調整
    final signImportance = _calculateSignImportance(fromSign, toSign, birthData);
    
    final totalScore = baseScore * signImportance;

    final explanation = '${planetName}從${fromSign}進入${toSign}，'
        '行星權重: ${planetWeight.toStringAsFixed(1)}，'
        '星座重要性: ${signImportance.toStringAsFixed(1)}';

    return EventScore(
      totalScore: totalScore,
      planetWeight: planetWeight,
      aspectStrength: 0.0,
      houseImportance: signImportance,
      timeAccuracy: 1.0,
      personalImpact: signImportance,
      explanation: explanation,
      calculatedAt: DateTime.now(),
    );
  }

  /// 計算行星換宮事件的評分
  EventScore calculateHouseChangeScore(
    String planetName,
    int fromHouse,
    int toHouse,
    BirthData birthData,
  ) {
    // 行星換宮的基礎分數
    final planetWeight = config.planetWeights[planetName] ?? 0.5;
    final fromHouseWeight = config.houseImportances[fromHouse] ?? 0.6;
    final toHouseWeight = config.houseImportances[toHouse] ?? 0.6;
    
    final houseImportance = math.max(fromHouseWeight, toHouseWeight);
    final totalScore = planetWeight * houseImportance * 25; // 基礎分數

    final explanation = '${planetName}從第${fromHouse}宮進入第${toHouse}宮，'
        '行星權重: ${planetWeight.toStringAsFixed(1)}，'
        '宮位重要性: ${houseImportance.toStringAsFixed(1)}';

    return EventScore(
      totalScore: totalScore,
      planetWeight: planetWeight,
      aspectStrength: 0.0,
      houseImportance: houseImportance,
      timeAccuracy: 1.0,
      personalImpact: houseImportance,
      explanation: explanation,
      calculatedAt: DateTime.now(),
    );
  }

  /// 計算行星權重
  double _calculatePlanetWeight(String planet1, String planet2) {
    final weight1 = config.planetWeights[planet1] ?? 0.5;
    final weight2 = config.planetWeights[planet2] ?? 0.5;
    
    // 使用較高的權重，並加上組合效應
    return math.max(weight1, weight2) + (weight1 * weight2 * 0.2);
  }

  /// 計算相位強度
  double _calculateAspectStrength(String aspectName, double orb) {
    final baseStrength = config.aspectStrengths[aspectName] ?? 0.3;
    final maxOrb = AspectDefinitions.getDefaultOrb(aspectName);
    
    // 根據容許度計算強度衰減
    final orbFactor = maxOrb > 0 ? math.max(0, (maxOrb - orb) / maxOrb) : 1.0;
    
    return baseStrength * orbFactor;
  }

  /// 計算宮位重要性
  double _calculateHouseImportance(int house1, int house2) {
    final importance1 = config.houseImportances[house1] ?? 0.6;
    final importance2 = config.houseImportances[house2] ?? 0.6;
    
    // 使用較高的重要性
    return math.max(importance1, importance2);
  }

  /// 計算時間精確度
  double _calculateTimeAccuracy(double actualOrb, double maxOrb) {
    if (maxOrb <= 0) return 1.0;
    
    // 使用指數衰減函數
    final accuracy = math.exp(-config.timeDecayFactor * (actualOrb / maxOrb));
    return math.max(0.1, accuracy); // 最小保持0.1
  }

  /// 計算個人化影響
  double _calculatePersonalImpact(
    AspectInfo aspect,
    BirthData birthData,
    bool isTransit,
  ) {
    double impact = 1.0;
    
    // 如果是行運，檢查是否觸及本命重要點位
    if (isTransit) {
      // 檢查是否涉及上升點、中天等重要點位
      if (_isImportantPoint(aspect.planet1) || _isImportantPoint(aspect.planet2)) {
        impact *= 1.3;
      }
      
      // 檢查是否涉及本命太陽或月亮
      if (_isLuminaries(aspect.planet1.name) || _isLuminaries(aspect.planet2.name)) {
        impact *= 1.2;
      }
    }
    
    return impact * config.personalImpactWeight;
  }

  /// 計算星座重要性
  double _calculateSignImportance(String fromSign, String toSign, BirthData birthData) {
    // 基礎重要性
    double importance = 1.0;
    
    // 如果進入的星座與本命重要行星星座相同，增加重要性
    // 這裡可以根據實際需求擴展
    
    return importance;
  }

  /// 計算總分數
  double _calculateTotalScore(
    double planetWeight,
    double aspectStrength,
    double houseImportance,
    double timeAccuracy,
    double personalImpact,
    bool isPrecise,
  ) {
    // 基礎分數計算
    double score = (planetWeight * aspectStrength * houseImportance * timeAccuracy * personalImpact) * 100;
    
    // 精確相位加成
    if (isPrecise) {
      score *= 1.2;
    }
    
    // 確保分數在合理範圍內
    return math.max(0, math.min(100, score));
  }

  /// 生成評分說明
  String _generateExplanation(
    AspectInfo aspect,
    double planetWeight,
    double aspectStrength,
    double houseImportance,
    double timeAccuracy,
    double personalImpact,
    double totalScore,
  ) {
    final buffer = StringBuffer();
    
    buffer.write('${aspect.planet1.name}${aspect.aspect}${aspect.planet2.name} ');
    buffer.write('(容許度: ${aspect.orb.toStringAsFixed(1)}°)\n');
    buffer.write('行星權重: ${planetWeight.toStringAsFixed(1)}\n');
    buffer.write('相位強度: ${aspectStrength.toStringAsFixed(1)}\n');
    buffer.write('宮位重要性: ${houseImportance.toStringAsFixed(1)}\n');
    buffer.write('時間精確度: ${timeAccuracy.toStringAsFixed(1)}\n');
    buffer.write('個人化影響: ${personalImpact.toStringAsFixed(1)}\n');
    buffer.write('總分: ${totalScore.toStringAsFixed(1)}');
    
    return buffer.toString();
  }

  /// 檢查是否為重要點位
  bool _isImportantPoint(PlanetPosition planet) {
    // 檢查是否為上升點、中天、下降點、天底
    return planet.name == '上升點' || 
           planet.name == '中天' || 
           planet.name == '下降點' || 
           planet.name == '天底';
  }

  /// 檢查是否為發光體（太陽、月亮）
  bool _isLuminaries(String planetName) {
    return planetName == '太陽' || planetName == '月亮';
  }
}
