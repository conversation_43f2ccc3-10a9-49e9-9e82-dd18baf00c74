import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/models/astrology/categorized_event_score.dart';
import '../../../data/models/astrology/categorized_timeline_data.dart';
import '../../../data/models/astrology/event_detection_config.dart';
import '../../../data/models/astrology/event_score.dart';
import '../../../data/models/astrology/event_timeline_data.dart';
import '../astrology_service.dart';
import '../calculations/event_score_calculator.dart';
import 'event_cache_service.dart';
import 'life_events_detector.dart';

/// 事件偵測服務
/// 
/// 負責偵測和分析占星事件，包括：
/// - 行運事件偵測
/// - 推運事件偵測
/// - 事件評分計算
/// - 時間軸資料生成
class EventDetectionService {
  /// 事件偵測配置
  final EventDetectionConfig config;
  
  /// 事件評分計算器
  late final EventScoreCalculator _scoreCalculator;

  /// 占星服務
  final AstrologyService _astrologyService;

  EventDetectionService({
    required this.config,
    required AstrologyService astrologyService,
  }) : _astrologyService = astrologyService {
    _scoreCalculator = EventScoreCalculator(
      config: EventScoreConfig.defaultConfig(),
    );
  }

  /// 偵測指定時間範圍內的所有事件
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回事件時間軸資料
  Future<EventTimelineData> detectEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = true,
  }) async {
    logger.d('開始偵測事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 嘗試從快取載入
    if (useCache) {
      final cachedData = await EventCacheService.getCachedEventData(
        birthData,
        startDate,
        endDate,
      );

      if (cachedData != null) {
        logger.d('使用快取資料');
        return cachedData;
      }
    }

    final dailyScores = <DailyEventScore>[];
    final allEvents = <AstroEvent>[];

    // 逐日偵測事件
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
      try {
        List<AstroEvent> dayEvents = await _detectDailyEvents(birthData, currentDate);
        final dayScores = dayEvents.map((event) {
          // 檢查是否有 AspectInfo 數據
          final aspectInfo = event.additionalData?['aspect'] as AspectInfo?;

          if (aspectInfo != null) {
            // 使用 AspectInfo 計算評分
            return _scoreCalculator.calculateAspectScore(
              aspectInfo,
              birthData,
              isTransit: event.type == AstroEventType.transitAspect,
              isPrecise: event.isExact ?? false,
            );
          } else {
            // 使用事件本身的分數創建簡單的 EventScore
            return EventScore(
              totalScore: event.score ?? 50.0,
              planetWeight: 1.0,
              aspectStrength: 1.0,
              houseImportance: 1.0,
              timeAccuracy: 1.0,
              personalImpact: 1.0,
              explanation: '基於事件內建分數: ${event.score ?? 50.0}',
              calculatedAt: DateTime.now(),
            );
          }
        }).toList();

        final dailyScore = DailyEventScore.fromEvents(currentDate, dayScores);
        dailyScores.add(dailyScore);
        allEvents.addAll(dayEvents);

        logger.d('${currentDate.toString().substring(0, 10)}: ${dayEvents.length} 個事件, 總分: ${dailyScore.totalScore.toStringAsFixed(1)}');
      } catch (e) {
        logger.e('偵測 $currentDate 事件時出錯: $e');
        // 添加空的每日評分以保持連續性
        dailyScores.add(DailyEventScore(
          date: currentDate,
          totalScore: 0,
          events: [],
          eventCount: 0,
        ));
      }

      currentDate = currentDate.add(const Duration(days: 1));
    }

    EventTimelineData timelineData = EventTimelineData.fromDailyScores(
      startDate,
      endDate,
      dailyScores,
      allEvents, // 傳遞收集到的所有事件
    );

    // 快取結果
    if (useCache) {
      await EventCacheService.cacheEventData(
        birthData,
        startDate,
        endDate,
        timelineData,
      );
    }

    logger.d('事件偵測完成: 總共 ${allEvents.length} 個事件');
    return timelineData;
  }

  /// 偵測分類事件（支援七大類事件分類）
  ///
  /// [birthData] 出生資料
  /// [startDate] 開始日期
  /// [endDate] 結束日期
  /// [useCache] 是否使用快取
  ///
  /// 返回分類事件時間軸資料
  Future<CategorizedTimelineData> detectCategorizedEvents(
    BirthData birthData,
    DateTime startDate,
    DateTime endDate, {
    bool useCache = true,
  }) async {
    logger.d('開始偵測分類事件: ${startDate.toString()} 到 ${endDate.toString()}');

    // 先獲取基本的事件資料
    final basicTimelineData = await detectEvents(
      birthData,
      startDate,
      endDate,
      useCache: useCache,
    );

    // 轉換為分類事件資料
    final categorizedDailyScores = <CategorizedEventScore>[];
    final allEvents = basicTimelineData.allEvents;

    // 為每一天創建分類評分
    for (final dailyScore in basicTimelineData.dailyScores) {
      // 獲取該天的事件
      final dayEvents = allEvents.where((event) =>
        _isSameDay(event.dateTime, dailyScore.date)
      ).toList();

      // 創建分類評分
      final categorizedScore = CategorizedEventScore.fromEvents(
        dailyScore.date,
        dailyScore.events,
        dayEvents,
      );

      categorizedDailyScores.add(categorizedScore);
    }

    // 創建分類時間軸資料
    final categorizedTimelineData = CategorizedTimelineData.fromDailyScores(
      startDate,
      endDate,
      categorizedDailyScores,
      allEvents,
    );

    logger.d('分類事件偵測完成: 總共 ${categorizedTimelineData.totalEventCount} 個事件');

    return categorizedTimelineData;
  }

  /// 檢查兩個日期是否為同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// 偵測單日事件（增強版）
  ///
  /// 使用多重星盤分析來偵測重大事件
  /// 1. 先分析本命盤判斷重大事件的機率
  /// 2. 再用推運相關的盤判斷重大事件發生的時間
  Future<List<AstroEvent>> _detectDailyEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 第一階段：計算各種星盤資料
      final chartDataMap = await _calculateMultipleCharts(birthData, date);

      // 第二階段：本命盤基礎分析 - 判斷重大事件機率
      final natalAnalysis = await _analyzeNatalChartForEventPotential(
        chartDataMap[ChartType.natal]!,
        birthData,
      );

      // 第三階段：推運盤分析 - 判斷事件發生時間
      final progressionAnalysis = await _analyzeProgressionChartsForTiming(
        chartDataMap,
        birthData,
        date,
        natalAnalysis,
      );

      // 第四階段：綜合分析生成事件
      final detectedEvents = await _synthesizeEventsFromAnalysis(
        chartDataMap,
        natalAnalysis,
        progressionAnalysis,
        birthData,
        date,
      );

      events.addAll(detectedEvents);

      // 第五階段：傳統事件偵測（保持向後相容）
      final traditionalEvents = await _detectTraditionalEvents(birthData, date);
      events.addAll(traditionalEvents);

    } catch (e) {
      logger.e('增強事件偵測失敗，回退到傳統方法: $e');
      // 回退到原始方法
      final fallbackEvents = await _detectTraditionalEvents(birthData, date);
      events.addAll(fallbackEvents);
    }

    // 過濾低分事件
    return events.where((event) =>
      (event.score ?? 0) >= config.minimumEventScore
    ).toList();
  }

  /// 傳統事件偵測方法（原始邏輯）
  Future<List<AstroEvent>> _detectTraditionalEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    // 1. 偵測行運事件
    if (config.enabledEventTypes.contains(AstroEventType.transitAspect)) {
      final transitEvents = await _detectTransitEvents(birthData, date);
      events.addAll(transitEvents);
    }

    // 2. 偵測推運事件
    if (config.enabledEventTypes.contains(AstroEventType.progressionAspect)) {
      final progressionEvents = await _detectProgressionEvents(birthData, date);
      events.addAll(progressionEvents);
    }

    // 3. 偵測太陽弧推運事件
    if (config.enabledEventTypes.contains(AstroEventType.solarArcAspect)) {
      final solarArcEvents = await _detectSolarArcEvents(birthData, date);
      events.addAll(solarArcEvents);
    }

    // 4. 偵測行星換座事件
    if (config.enabledEventTypes.contains(AstroEventType.planetSignChange)) {
      final signChangeEvents = await _detectSignChangeEvents(birthData, date);
      events.addAll(signChangeEvents);
    }

    // 5. 偵測行星換宮事件
    if (config.enabledEventTypes.contains(AstroEventType.planetHouseChange)) {
      final houseChangeEvents = await _detectHouseChangeEvents(birthData, date);
      events.addAll(houseChangeEvents);
    }

    // 6. 偵測人生重大事件
    final majorLifeEvents = await _detectMajorLifeEvents(birthData, date);
    events.addAll(majorLifeEvents);

    return events;
  }

  /// 計算多重星盤資料
  Future<Map<ChartType, ChartData>> _calculateMultipleCharts(
    BirthData birthData,
    DateTime date,
  ) async {
    final chartDataMap = <ChartType, ChartData>{};

    try {
      // 1. 本命盤
      final natalChart = ChartData(
        chartType: ChartType.natal,
        primaryPerson: birthData,
        specificDate: birthData.dateTime,
      );
      chartDataMap[ChartType.natal] = await AstrologyService.calculateChartData(natalChart);

      // 2. 行運盤
      final transitChart = ChartData(
        chartType: ChartType.transit,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.transit] = await AstrologyService.calculateChartData(transitChart);

      // 3. 次限推運盤
      final secondaryChart = ChartData(
        chartType: ChartType.secondaryProgression,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.secondaryProgression] = await AstrologyService.calculateChartData(secondaryChart);

      // 4. 太陽弧推運盤
      final solarArcChart = ChartData(
        chartType: ChartType.solarArcDirection,
        primaryPerson: birthData,
        specificDate: date,
      );
      chartDataMap[ChartType.solarArcDirection] = await AstrologyService.calculateChartData(solarArcChart);

      logger.d('成功計算 ${chartDataMap.length} 個星盤');

    } catch (e) {
      logger.e('計算星盤資料時出錯: $e');
      rethrow;
    }

    return chartDataMap;
  }

  /// 分析本命盤的重大事件潛力
  Future<Map<String, dynamic>> _analyzeNatalChartForEventPotential(
    ChartData natalChart,
    BirthData birthData,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 分析各個生活領域的敏感度
      analysis['relationship_sensitivity'] = _analyzeRelationshipSensitivity(natalChart);
      logger.d('分析感情敏感度: ${analysis['relationship_sensitivity']}');

      analysis['career_sensitivity'] = _analyzeCareerSensitivity(natalChart);
      logger.d('分析事業敏感度: ${analysis['career_sensitivity']}');

      analysis['financial_sensitivity'] = _analyzeFinancialSensitivity(natalChart);
      logger.d('分析財務敏感度: ${analysis['financial_sensitivity']}');

      analysis['health_sensitivity'] = _analyzeHealthSensitivity(natalChart);
      logger.d('分析健康敏感度: ${analysis['health_sensitivity']}');

      analysis['learning_sensitivity'] = _analyzeLearningGrowthSensitivity(natalChart);
      logger.d('分析學習敏感度: ${analysis['learning_sensitivity']}');

      analysis['relocation_sensitivity'] = _analyzeRelocationSensitivity(natalChart);
      logger.d('分析搬遷敏感度: ${analysis['relocation_sensitivity']}');

      analysis['spiritual_sensitivity'] = _analyzeSpiritualTransformationSensitivity(natalChart);
      logger.d('分析心靈敏感度: ${analysis['spiritual_sensitivity']}');

      // 計算整體敏感度分數
      analysis['overall_sensitivity'] = _calculateOverallSensitivity(analysis);
      logger.d('本命盤分析完成，整體敏感度: ${analysis['overall_sensitivity']}');

    } catch (e) {
      logger.e('本命盤分析失敗: $e');
      // 返回預設值
      analysis['overall_sensitivity'] = 50.0;
    }

    return analysis;
  }

  /// 分析感情與人際關係敏感度
  double _analyzeRelationshipSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    // 檢查7宮、5宮、4宮的行星配置
    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];
    final aspects = natalChart.aspects ?? [];

    // 7宮（伴侶關係）
    if (houses != null && houses.cusps.length > 6) {
      final house7Cusp = houses.cusps[6]; // 第7宮宮頭度數
      // 檢查7宮內的行星
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 7)) {
          if (planet.name == '金星' || planet.name == '月亮' || planet.name == '火星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    // 檢查金星、月亮、火星的相位
    for (final aspect in aspects) {
      if (_isRelationshipPlanet(aspect.planet1.name) || _isRelationshipPlanet(aspect.planet2.name)) {
        if (_isChallengingAspect(aspect.aspect)) {
          sensitivity += 10.0;
        } else if (_isHarmoniousAspect(aspect.aspect)) {
          sensitivity += 5.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析事業與工作轉折敏感度
  double _analyzeCareerSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];
    final aspects = natalChart.aspects ?? [];

    // 10宮（事業）和6宮（工作）
    if (houses != null && houses.cusps.length > 9) {
      // 檢查重要行星在事業宮位
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 10)) {
          if (planet.name == '太陽' || planet.name == '木星' || planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
        if (_isPlanetInHouse(planet, 6)) {
          if (planet.name == '火星' || planet.name == '水星') {
            sensitivity += 10.0;
          } else {
            sensitivity += 5.0;
          }
        }
      }
    }

    // 檢查太陽、土星、木星的相位
    for (final aspect in aspects) {
      if (_isCareerPlanet(aspect.planet1.name) || _isCareerPlanet(aspect.planet2.name)) {
        if (_isChallengingAspect(aspect.aspect)) {
          sensitivity += 12.0;
        } else if (_isHarmoniousAspect(aspect.aspect)) {
          sensitivity += 6.0;
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析財務狀況敏感度
  double _analyzeFinancialSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 2宮（個人財務）和8宮（共同財務）
    if (houses != null && houses.cusps.length > 7) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 2) || _isPlanetInHouse(planet, 8)) {
          if (planet.name == '金星' || planet.name == '木星' || planet.name == '土星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析健康與身體狀況敏感度
  double _analyzeHealthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 6宮（健康）和8宮（生死）
    if (houses != null && houses.cusps.length > 7) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 6) || _isPlanetInHouse(planet, 8)) {
          if (planet.name == '火星' || planet.name == '土星' || planet.name == '天王星' || planet.name == '冥王星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析學習與成長敏感度
  double _analyzeLearningGrowthSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 3宮（學習）和9宮（高等教育、哲學）
    if (houses != null && houses.cusps.length > 8) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 3) || _isPlanetInHouse(planet, 9)) {
          if (planet.name == '水星' || planet.name == '木星' || planet.name == '天王星') {
            sensitivity += 12.0;
          } else {
            sensitivity += 6.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析搬遷與環境變動敏感度
  double _analyzeRelocationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 4宮（家庭、居住）和9宮（遠行）
    if (houses != null && houses.cusps.length > 8) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 4) || _isPlanetInHouse(planet, 9)) {
          if (planet.name == '天王星' || planet.name == '木星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 分析心靈與命運轉折敏感度
  double _analyzeSpiritualTransformationSensitivity(ChartData natalChart) {
    double sensitivity = 0.0;

    final houses = natalChart.houses;
    final planets = natalChart.planets ?? [];

    // 8宮（轉化）和12宮（靈性）
    if (houses != null && houses.cusps.length > 11) {
      for (final planet in planets) {
        if (_isPlanetInHouse(planet, 8) || _isPlanetInHouse(planet, 12)) {
          if (planet.name == '冥王星' || planet.name == '海王星' || planet.name == '土星') {
            sensitivity += 15.0;
          } else {
            sensitivity += 8.0;
          }
        }
      }
    }

    return sensitivity.clamp(0.0, 100.0);
  }

  /// 計算整體敏感度
  double _calculateOverallSensitivity(Map<String, dynamic> analysis) {
    double total = 0.0;
    int count = 0;

    for (final key in analysis.keys) {
      if (key.endsWith('_sensitivity') && analysis[key] is double) {
        total += analysis[key] as double;
        count++;
      }
    }

    return count > 0 ? total / count : 50.0;
  }

  /// 分析推運盤的時間觸發
  Future<Map<String, dynamic>> _analyzeProgressionChartsForTiming(
    Map<ChartType, ChartData> chartDataMap,
    BirthData birthData,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final analysis = <String, dynamic>{};

    try {
      // 行運分析
      if (chartDataMap.containsKey(ChartType.transit)) {
        analysis['transit_triggers'] = await _analyzeTransitTriggers(
          chartDataMap[ChartType.transit]!,
          natalAnalysis,
        );
      }

      // 次限推運分析
      if (chartDataMap.containsKey(ChartType.secondaryProgression)) {
        analysis['progression_triggers'] = await _analyzeProgressionTriggers(
          chartDataMap[ChartType.secondaryProgression]!,
          natalAnalysis,
        );
      }

      // 太陽弧推運分析
      if (chartDataMap.containsKey(ChartType.solarArcDirection)) {
        analysis['solar_arc_triggers'] = await _analyzeSolarArcTriggers(
          chartDataMap[ChartType.solarArcDirection]!,
          natalAnalysis,
        );
      }

      logger.d('推運分析完成');
    } catch (e) {
      logger.e('推運分析失敗: $e');
    }

    return analysis;
  }

  /// 分析行運觸發
  Future<List<Map<String, dynamic>>> _analyzeTransitTriggers(
    ChartData transitChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];
    // 檢查行運行星與本命行星的相位
    transitChart.aspects?.forEach((aspect) {
      print('行運相位: ${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}');
      if (aspect.orb <= 3.0) { // 3度容許度
        final aspectType = aspect.aspect;

        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'transit_aspect',
            'transit_planet': aspect.planet1.name,
            'natal_planet': aspect.planet2.name,
            'aspect': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet1.name,
              aspect.planet2.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });
    return triggers;
  }

  /// 分析推運觸發
  Future<List<Map<String, dynamic>>> _analyzeProgressionTriggers(
    ChartData progressionChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];
    // 檢查推運行星與本命行星的相位
    progressionChart.aspects?.forEach((aspect) {
      print('次限推運相位: ${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}');
      if (aspect.orb <= 1.0) { // 1度容許度
        final aspectType = aspect.aspect;
        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'progression_aspect',
            'progression_planet': aspect.planet1.name,
            'natal_planet': aspect.planet2.name,
            'aspect': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet1.name,
              aspect.planet2.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });

    return triggers;
  }

  /// 分析太陽弧推運觸發
  Future<List<Map<String, dynamic>>> _analyzeSolarArcTriggers(
    ChartData solarArcChart,
    Map<String, dynamic> natalAnalysis,
  ) async {
    final triggers = <Map<String, dynamic>>[];

    // 檢查太陽弧推運行星與本命行星的相位
    solarArcChart.aspects?.forEach((aspect) {
      print('次限推運相位: ${aspect.planet1.name} ${aspect.aspect} ${aspect.planet2.name}');
      if (aspect.orb <= 1.0) { // 1度容許度
        final aspectType = aspect.aspect;
        if (_isSignificantAspect(aspectType)) {
          final trigger = {
            'type': 'solar_arc_aspect',
            'solar_arc_planet': aspect.planet1.name,
            'natal_planet': aspect.planet2.name,
            'aspect': aspectType,
            'orb': aspect.orb,
            'strength': _calculateAspectStrength(aspectType, aspect.orb),
            'event_potential': _calculateEventPotential(
              aspect.planet1.name,
              aspect.planet2.name,
              aspectType,
              natalAnalysis,
              planet1House: aspect.planet1.house,
              planet2House: aspect.planet2.house,
            ),
          };
          triggers.add(trigger);
        }
      }
    });
    return triggers;
  }

  /// 綜合分析生成事件
  Future<List<AstroEvent>> _synthesizeEventsFromAnalysis(
    Map<ChartType, ChartData> chartDataMap,
    Map<String, dynamic> natalAnalysis,
    Map<String, dynamic> progressionAnalysis,
    BirthData birthData,
    DateTime date,
  ) async {
    final events = <AstroEvent>[];

    try {
      // 處理行運觸發
      if (progressionAnalysis.containsKey('transit_triggers')) {
        final transitTriggers = progressionAnalysis['transit_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in transitTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理推運觸發
      if (progressionAnalysis.containsKey('progression_triggers')) {
        final progressionTriggers = progressionAnalysis['progression_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in progressionTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

      // 處理太陽弧推運觸發
      if (progressionAnalysis.containsKey('solar_arc_triggers')) {
        final solarArcTriggers = progressionAnalysis['solar_arc_triggers'] as List<Map<String, dynamic>>;
        for (final trigger in solarArcTriggers) {
          final event = await _createEventFromTrigger(trigger, date, natalAnalysis);
          if (event != null) events.add(event);
        }
      }

    } catch (e) {
      logger.e('綜合分析生成事件失敗: $e');
    }

    return events;
  }

  /// 從觸發器創建事件
  Future<AstroEvent?> _createEventFromTrigger(
    Map<String, dynamic> trigger,
    DateTime date,
    Map<String, dynamic> natalAnalysis,
  ) async {
    try {
      final eventPotential = trigger['event_potential'] as Map<String, dynamic>;
      final eventType = eventPotential['primary_event_type'] as String;
      final score = eventPotential['score'] as double;

      if (score < config.minimumEventScore) return null;

      final title = _generateEventTitle(trigger, eventType);
      final description = _generateEventDescription(trigger, eventType, natalAnalysis);

      return AstroEvent(
        id: 'enhanced_${date.millisecondsSinceEpoch}_${trigger.hashCode}',
        title: title,
        description: description,
        dateTime: date,
        type: _mapEventTypeToAstroEventType(eventType),
        color: _getEventTypeColor(eventType),
        icon: _getEventTypeIcon(eventType),
        importance: _calculateImportanceLevel(score),
        isVisible: true,
        score: score,
        eventImportance: EventImportanceExtension.fromScore(score),
        involvedPlanets: _extractInvolvedPlanets(trigger),
        aspectType: trigger['aspect'] as String?,
        orb: trigger['orb'] as double?,
        isExact: (trigger['orb'] as double?) != null && (trigger['orb'] as double) < 0.5,
        additionalData: {
          'trigger_data': trigger,
          'event_category': eventType,
          'analysis_source': 'enhanced_detection',
        },
      );
    } catch (e) {
      logger.e('創建事件失敗: $e');
      return null;
    }
  }

  // 輔助方法
  bool _isPlanetInHouse(PlanetPosition planet, int houseNumber) {
    // 檢查行星是否在指定宮位
    return planet.house == houseNumber;
  }

  bool _isRelationshipPlanet(String planetName) {
    return ['金星', '月亮', '火星'].contains(planetName);
  }

  bool _isCareerPlanet(String planetName) {
    return ['太陽', '土星', '木星'].contains(planetName);
  }

  /// 判斷是否為健康相關行星
  bool _isHealthPlanet(String planetName) {
    return ['火星', '土星', '天王星', '冥王星', '月亮'].contains(planetName);
  }

  /// 判斷是否為財務相關行星
  bool _isFinancePlanet(String planetName) {
    return ['金星', '木星', '土星', '冥王星'].contains(planetName);
  }

  /// 判斷是否為學習相關行星
  bool _isEducationPlanet(String planetName) {
    return ['水星', '木星', '天王星'].contains(planetName);
  }

  /// 判斷是否為家庭相關行星
  bool _isFamilyPlanet(String planetName) {
    return ['月亮', '太陽', '土星', '冥王星'].contains(planetName);
  }

  /// 判斷是否為心靈相關行星
  bool _isSpiritualPlanet(String planetName) {
    return ['海王星', '冥王星', '木星', '月亮'].contains(planetName);
  }

  /// 判斷是否為感情相關宮位
  bool _isRelationshipHouse(int? house) {
    if (house == null) return false;
    return [5, 7, 8, 11].contains(house); // 第5宮(戀愛)、第7宮(伴侶)、第8宮(親密關係)、第11宮(友情)
  }

  /// 判斷是否為事業相關宮位
  bool _isCareerHouse(int? house) {
    if (house == null) return false;
    return [2, 6, 10].contains(house); // 第2宮(財富)、第6宮(工作)、第10宮(事業)
  }

  /// 判斷是否為健康相關宮位
  bool _isHealthHouse(int? house) {
    if (house == null) return false;
    return [1, 6, 8, 12].contains(house); // 第1宮(體質)、第6宮(健康)、第8宮(醫療)、第12宮(慢性病)
  }

  /// 判斷是否為學習相關宮位
  bool _isEducationHouse(int? house) {
    if (house == null) return false;
    return [3, 9].contains(house); // 第3宮(基礎學習)、第9宮(高等教育)
  }

  /// 判斷是否為家庭相關宮位
  bool _isFamilyHouse(int? house) {
    if (house == null) return false;
    return [4, 10].contains(house); // 第4宮(家庭)、第10宮(父母)
  }

  /// 判斷是否為財務相關宮位
  bool _isFinanceHouse(int? house) {
    if (house == null) return false;
    return [2, 8].contains(house); // 第2宮(個人財富)、第8宮(共同財產)
  }

  /// 判斷是否為心靈相關宮位
  bool _isSpiritualHouse(int? house) {
    if (house == null) return false;
    return [8, 9, 12].contains(house); // 第8宮(轉化)、第9宮(哲學)、第12宮(靈性)
  }

  /// 轉換行星名稱為中文
  String _translatePlanetName(String planetName) {
    final translations = {
      'Sun': '太陽',
      'Moon': '月亮',
      'Mercury': '水星',
      'Venus': '金星',
      'Mars': '火星',
      'Jupiter': '木星',
      'Saturn': '土星',
      'Uranus': '天王星',
      'Neptune': '海王星',
      'Pluto': '冥王星',
    };
    return translations[planetName] ?? planetName;
  }

  /// 為傳統事件判斷事件類別
  String _determineEventCategoryForTraditionalEvent(
    String transitPlanet,
    String natalPlanet,
    String aspectType,
    int? natalHouse,
  ) {
    // 轉換行星名稱為中文
    final transitPlanetCN = _translatePlanetName(transitPlanet);
    final natalPlanetCN = _translatePlanetName(natalPlanet);
    // 感情事件判斷
    if (_isRelationshipPlanet(transitPlanetCN) ||
        _isRelationshipPlanet(natalPlanetCN) ||
        _isRelationshipHouse(natalHouse)) {
      return 'relationship';
    }

    // 事業事件判斷
    if (_isCareerPlanet(transitPlanetCN) ||
        _isCareerPlanet(natalPlanetCN) ||
        _isCareerHouse(natalHouse)) {
      return 'career';
    }

    // 健康事件判斷
    if (_isHealthPlanet(transitPlanetCN) ||
        _isHealthPlanet(natalPlanetCN) ||
        _isHealthHouse(natalHouse)) {
      return 'health';
    }

    // 財務事件判斷
    if (_isFinancePlanet(transitPlanetCN) ||
        _isFinancePlanet(natalPlanetCN) ||
        _isFinanceHouse(natalHouse)) {
      return 'finance';
    }

    // 學習事件判斷
    if (_isEducationPlanet(transitPlanetCN) ||
        _isEducationPlanet(natalPlanetCN) ||
        _isEducationHouse(natalHouse)) {
      return 'education';
    }

    // 家庭事件判斷
    if (_isFamilyPlanet(transitPlanetCN) ||
        _isFamilyPlanet(natalPlanetCN) ||
        _isFamilyHouse(natalHouse)) {
      return 'family';
    }

    // 心靈事件判斷
    if (_isSpiritualPlanet(transitPlanetCN) ||
        _isSpiritualPlanet(natalPlanetCN) ||
        _isSpiritualHouse(natalHouse)) {
      return 'spiritual';
    }

    // 預設為心靈類別
    return 'spiritual';
  }

  bool _isChallengingAspect(String aspectType) {
    return ['刑', '沖', '半刑', '八分相'].contains(aspectType);
  }

  bool _isHarmoniousAspect(String aspectType) {
    return ['合', '拱', '六分相'].contains(aspectType);
  }

  double _calculateOrb(double longitude1, double longitude2) {
    double diff = (longitude1 - longitude2).abs();
    if (diff > 180) diff = 360 - diff;
    return diff;
  }

  String _determineAspectType(double longitude1, double longitude2) {
    final orb = _calculateOrb(longitude1, longitude2);

    if (orb <= 8) return '合';
    if ((orb - 60).abs() <= 6) return '六分相';
    if ((orb - 90).abs() <= 8) return '刑';
    if ((orb - 120).abs() <= 8) return '拱';
    if ((orb - 180).abs() <= 8) return '沖';

    return '無相位';
  }

  bool _isSignificantAspect(String aspectType) {
    return aspectType != '無相位';
  }

  double _calculateAspectStrength(String aspectType, double orb) {
    final baseStrength = {
      '合': 100.0,
      '沖': 90.0,
      '刑': 80.0,
      '拱': 70.0,
      '六分相': 60.0,
    }[aspectType] ?? 0.0;

    // 根據容許度調整強度
    final orbFactor = (8.0 - orb) / 8.0;
    return baseStrength * orbFactor;
  }

  Map<String, dynamic> _calculateEventPotential(
    String planet1,
    String planet2,
    String aspectType,
    Map<String, dynamic> natalAnalysis, {
    int? planet1House,
    int? planet2House,
  }) {
    // 根據行星組合、宮位和相位類型判斷事件類型和強度
    final eventTypes = <String, double>{};

    // 感情事件 - 考慮行星和宮位
    final isRelationshipEvent = _isRelationshipPlanet(planet1) ||
                               _isRelationshipPlanet(planet2) ||
                               _isRelationshipHouse(planet1House) ||
                               _isRelationshipHouse(planet2House);

    if (isRelationshipEvent) {
      final sensitivity = natalAnalysis['relationship_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及感情行星和感情宮位，增加權重
      if ((_isRelationshipPlanet(planet1) || _isRelationshipPlanet(planet2)) &&
          (_isRelationshipHouse(planet1House) || _isRelationshipHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['relationship'] = sensitivity * multiplier;
    }

    // 事業事件 - 考慮行星和宮位
    final isCareerEvent = _isCareerPlanet(planet1) ||
                         _isCareerPlanet(planet2) ||
                         _isCareerHouse(planet1House) ||
                         _isCareerHouse(planet2House);

    if (isCareerEvent) {
      final sensitivity = natalAnalysis['career_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及事業行星和事業宮位，增加權重
      if ((_isCareerPlanet(planet1) || _isCareerPlanet(planet2)) &&
          (_isCareerHouse(planet1House) || _isCareerHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['career'] = sensitivity * multiplier;
    }

    // 健康事件 - 考慮行星和宮位
    final isHealthEvent = _isHealthPlanet(planet1) ||
                         _isHealthPlanet(planet2) ||
                         _isHealthHouse(planet1House) ||
                         _isHealthHouse(planet2House);

    if (isHealthEvent) {
      final sensitivity = natalAnalysis['health_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.7;

      // 如果同時涉及健康行星和健康宮位，增加權重
      if ((_isHealthPlanet(planet1) || _isHealthPlanet(planet2)) &&
          (_isHealthHouse(planet1House) || _isHealthHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['health'] = sensitivity * multiplier;
    }

    // 財務事件 - 考慮行星和宮位
    final isFinanceEvent = _isFinancePlanet(planet1) ||
                          _isFinancePlanet(planet2) ||
                          _isFinanceHouse(planet1House) ||
                          _isFinanceHouse(planet2House);

    if (isFinanceEvent) {
      final sensitivity = natalAnalysis['finance_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及財務行星和財務宮位，增加權重
      if ((_isFinancePlanet(planet1) || _isFinancePlanet(planet2)) &&
          (_isFinanceHouse(planet1House) || _isFinanceHouse(planet2House))) {
        multiplier = 1.2;
      }

      eventTypes['finance'] = sensitivity * multiplier;
    }

    // 學習事件 - 考慮行星和宮位
    final isEducationEvent = _isEducationPlanet(planet1) ||
                            _isEducationPlanet(planet2) ||
                            _isEducationHouse(planet1House) ||
                            _isEducationHouse(planet2House);

    if (isEducationEvent) {
      final sensitivity = natalAnalysis['education_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.7;

      // 如果同時涉及學習行星和學習宮位，增加權重
      if ((_isEducationPlanet(planet1) || _isEducationPlanet(planet2)) &&
          (_isEducationHouse(planet1House) || _isEducationHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['education'] = sensitivity * multiplier;
    }

    // 家庭事件 - 考慮行星和宮位
    final isFamilyEvent = _isFamilyPlanet(planet1) ||
                         _isFamilyPlanet(planet2) ||
                         _isFamilyHouse(planet1House) ||
                         _isFamilyHouse(planet2House);

    if (isFamilyEvent) {
      final sensitivity = natalAnalysis['family_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.8;

      // 如果同時涉及家庭行星和家庭宮位，增加權重
      if ((_isFamilyPlanet(planet1) || _isFamilyPlanet(planet2)) &&
          (_isFamilyHouse(planet1House) || _isFamilyHouse(planet2House))) {
        multiplier = 1.1;
      }

      eventTypes['family'] = sensitivity * multiplier;
    }

    // 心靈事件 - 考慮行星和宮位
    final isSpiritualEvent = _isSpiritualPlanet(planet1) ||
                            _isSpiritualPlanet(planet2) ||
                            _isSpiritualHouse(planet1House) ||
                            _isSpiritualHouse(planet2House);

    if (isSpiritualEvent) {
      final sensitivity = natalAnalysis['spiritual_sensitivity'] as double? ?? 50.0;
      double multiplier = 0.9;

      // 如果同時涉及心靈行星和心靈宮位，增加權重
      if ((_isSpiritualPlanet(planet1) || _isSpiritualPlanet(planet2)) &&
          (_isSpiritualHouse(planet1House) || _isSpiritualHouse(planet2House))) {
        multiplier = 1.3;
      }

      eventTypes['spiritual'] = sensitivity * multiplier;
    }

    // 找出最高分的事件類型
    String primaryEventType = 'general';
    double maxScore = 0.0;

    for (final entry in eventTypes.entries) {
      if (entry.value > maxScore) {
        maxScore = entry.value;
        primaryEventType = entry.key;
      }
    }

    return {
      'primary_event_type': primaryEventType,
      'score': maxScore,
      'all_potentials': eventTypes,
    };
  }

  String _generateEventTitle(Map<String, dynamic> trigger, String eventType) {
    final planet1 = trigger['transit_planet'] ?? trigger['progression_planet'] ?? trigger['solar_arc_planet'];
    final planet2 = trigger['natal_planet'];
    final aspect = trigger['aspect'];

    final eventTypeNames = {
      'relationship': '感情變化',
      'career': '事業轉折',
      'financial': '財務變動',
      'health': '健康狀況',
      'learning': '學習成長',
      'relocation': '環境變動',
      'spiritual': '心靈轉化',
    };

    final eventTypeName = eventTypeNames[eventType] ?? '重要事件';
    return '$eventTypeName - $planet1 $aspect $planet2';
  }

  String _generateEventDescription(
    Map<String, dynamic> trigger,
    String eventType,
    Map<String, dynamic> natalAnalysis,
  ) {
    final planet1 = trigger['transit_planet'] ?? trigger['progression_planet'] ?? trigger['solar_arc_planet'];
    final planet2 = trigger['natal_planet'];
    final aspect = trigger['aspect'];
    final strength = trigger['strength'] as double;

    final descriptions = {
      'relationship': '這個相位可能帶來感情關係的重要變化，需要特別關注伴侶關係和人際互動。',
      'career': '事業發展可能面臨重要轉折點，是時候重新評估職業方向和目標。',
      'financial': '財務狀況可能出現變化，建議謹慎處理投資和理財決策。',
      'health': '需要特別關注身體健康，建議進行健康檢查或調整生活習慣。',
      'learning': '這是學習新知識或技能的好時機，有助於個人成長和發展。',
      'relocation': '可能面臨搬遷或環境變化的機會，需要仔細考慮相關決定。',
      'spiritual': '內在心靈可能經歷重要轉化，是深度自我反省和成長的時期。',
    };

    final baseDescription = descriptions[eventType] ?? '這是一個重要的占星事件，值得關注。';
    return '$baseDescription\n\n相位強度：${strength.toStringAsFixed(1)}';
  }

  AstroEventType _mapEventTypeToAstroEventType(String eventType) {
    switch (eventType) {
      case 'relationship':
        return AstroEventType.transitAspect;
      case 'career':
        return AstroEventType.transitAspect;
      default:
        return AstroEventType.transitAspect;
    }
  }

  Color _getEventTypeColor(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Colors.pink;
      case 'career':
        return Colors.blue;
      case 'financial':
        return Colors.green;
      case 'health':
        return Colors.red;
      case 'learning':
        return Colors.purple;
      case 'relocation':
        return Colors.orange;
      case 'spiritual':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  IconData _getEventTypeIcon(String eventType) {
    switch (eventType) {
      case 'relationship':
        return Icons.favorite;
      case 'career':
        return Icons.work;
      case 'financial':
        return Icons.attach_money;
      case 'health':
        return Icons.health_and_safety;
      case 'learning':
        return Icons.school;
      case 'relocation':
        return Icons.home;
      case 'spiritual':
        return Icons.self_improvement;
      default:
        return Icons.star;
    }
  }

  int _calculateImportanceLevel(double score) {
    if (score >= 80) return 5;
    if (score >= 60) return 4;
    if (score >= 40) return 3;
    if (score >= 20) return 2;
    return 1;
  }

  List<String> _extractInvolvedPlanets(Map<String, dynamic> trigger) {
    final planets = <String>[];

    if (trigger.containsKey('transit_planet')) {
      planets.add(trigger['transit_planet'] as String);
    }
    if (trigger.containsKey('progression_planet')) {
      planets.add(trigger['progression_planet'] as String);
    }
    if (trigger.containsKey('solar_arc_planet')) {
      planets.add(trigger['solar_arc_planet'] as String);
    }
    if (trigger.containsKey('natal_planet')) {
      planets.add(trigger['natal_planet'] as String);
    }

    return planets;
  }

  /// 偵測行運事件
  Future<List<AstroEvent>> _detectTransitEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算本命盤行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算行運盤行星位置
      final transitPlanets = await AstrologyService.calculatePlanetPositions(
        date,
        birthData.latitude,
        birthData.longitude,
      );
      
      final events = <AstroEvent>[];

      // 簡化的行運相位分析（實際實作需要更複雜的相位計算）
      // 這裡提供基本框架，實際使用時需要完整的相位計算邏輯
      for (final transitPlanet in transitPlanets) {
        for (final natalPlanet in natalPlanets) {
          // 簡化的相位檢查（實際需要使用 AspectCalculator）
          final angleDiff = (transitPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          // 檢查主要相位（合相、對沖、四分相、三分相）
          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 8) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 8) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 6) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 6) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 3.0) {
            // 創建模擬的 AspectInfo 用於評分
            final mockAspect = {
              'planet1': {'name': transitPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            // 判斷事件類別
            final eventCategory = _determineEventCategoryForTraditionalEvent(
              transitPlanet.name,
              natalPlanet.name,
              aspectType,
              natalPlanet.house,
            );

            final event = AstroEvent(
              id: _generateEventId('transit', mockAspect, date),
              title: '${transitPlanet.name}${aspectType}${natalPlanet.name}',
              description: '行運${transitPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.transitAspect,
              importance: 3, // 預設重要性
              isVisible: true,
              color: AstroEventType.transitAspect.defaultColor,
              icon: AstroEventType.transitAspect.icon,
              score: 50.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(50.0),
              involvedPlanets: [transitPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house, natalPlanet.house],
              involvedSigns: [transitPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 1.0,
              additionalData: {
                'transitPlanet': transitPlanet,
                'natalPlanet': natalPlanet,
                'isTransit': true,
                'event_category': eventCategory, // 添加事件分類
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測行運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測推運事件
  Future<List<AstroEvent>> _detectProgressionEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算推運日期（次限推運：一天等於一年）
      final daysSinceBirth = date.difference(birthData.dateTime).inDays;
      final progressionDate = birthData.dateTime.add(Duration(days: daysSinceBirth ~/ 365));

      // 計算推運行星位置
      final progressionPlanets = await AstrologyService.calculatePlanetPositions(
        progressionDate,
        birthData.latitude,
        birthData.longitude,
      );

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的推運相位分析
      for (final progressionPlanet in progressionPlanets) {
        for (final natalPlanet in natalPlanets) {
          final angleDiff = (progressionPlanet.longitude - natalPlanet.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': progressionPlanet.name},
              'planet2': {'name': natalPlanet.name},
              'aspect': aspectType,
              'orb': orb,
            };

            // 判斷事件類別
            final eventCategory = _determineEventCategoryForTraditionalEvent(
              progressionPlanet.name,
              natalPlanet.name,
              aspectType,
              natalPlanet.house,
            );

            final event = AstroEvent(
              id: _generateEventId('progression', mockAspect, date),
              title: '推運${progressionPlanet.name}${aspectType}${natalPlanet.name}',
              description: '推運${progressionPlanet.name}與本命${natalPlanet.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.progressionAspect,
              importance: 4, // 推運事件通常較重要
              isVisible: true,
              color: AstroEventType.progressionAspect.defaultColor,
              icon: AstroEventType.progressionAspect.icon,
              score: 60.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(60.0),
              involvedPlanets: [progressionPlanet.name, natalPlanet.name],
              involvedHouses: [natalPlanet.house, natalPlanet.house],
              involvedSigns: [progressionPlanet.sign, natalPlanet.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'progressionPlanet': progressionPlanet,
                'natalPlanet': natalPlanet,
                'isProgression': true,
                'event_category': eventCategory, // 添加事件分類
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測太陽弧推運事件
  Future<List<AstroEvent>> _detectSolarArcEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    try {
      // 計算太陽弧推運（太陽的移動度數應用到所有行星）
      final yearsSinceBirth = date.difference(birthData.dateTime).inDays / 365.25;
      final solarArcDegrees = yearsSinceBirth; // 簡化：每年約1度

      // 計算本命行星位置
      final natalPlanets = await AstrologyService.calculatePlanetPositions(
        birthData.dateTime,
        birthData.latitude,
        birthData.longitude,
      );

      final events = <AstroEvent>[];

      // 簡化的太陽弧推運相位分析
      for (final natalPlanet1 in natalPlanets) {
        for (final natalPlanet2 in natalPlanets) {
          if (natalPlanet1.name == natalPlanet2.name) continue;

          // 計算太陽弧推運後的位置
          final solarArcLongitude = (natalPlanet1.longitude + solarArcDegrees) % 360;
          final angleDiff = (solarArcLongitude - natalPlanet2.longitude).abs();
          final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;

          String? aspectType;
          double orb = 0;

          if ((normalizedAngle - 0).abs() <= 1) {
            aspectType = '合相';
            orb = (normalizedAngle - 0).abs();
          } else if ((normalizedAngle - 180).abs() <= 1) {
            aspectType = '對沖';
            orb = (normalizedAngle - 180).abs();
          } else if ((normalizedAngle - 90).abs() <= 1) {
            aspectType = '四分相';
            orb = (normalizedAngle - 90).abs();
          } else if ((normalizedAngle - 120).abs() <= 1) {
            aspectType = '三分相';
            orb = (normalizedAngle - 120).abs();
          }

          if (aspectType != null && orb <= 1.0) {
            final mockAspect = {
              'planet1': {'name': natalPlanet1.name},
              'planet2': {'name': natalPlanet2.name},
              'aspect': aspectType,
              'orb': orb,
            };

            // 判斷事件類別
            final eventCategory = _determineEventCategoryForTraditionalEvent(
              natalPlanet1.name,
              natalPlanet2.name,
              aspectType,
              natalPlanet2.house,
            );

            final event = AstroEvent(
              id: _generateEventId('solarArc', mockAspect, date),
              title: '太陽弧${natalPlanet1.name}${aspectType}${natalPlanet2.name}',
              description: '太陽弧推運${natalPlanet1.name}與本命${natalPlanet2.name}形成${aspectType}',
              dateTime: date,
              type: AstroEventType.solarArcAspect,
              importance: 4, // 太陽弧推運事件較重要
              isVisible: true,
              color: AstroEventType.solarArcAspect.defaultColor,
              icon: AstroEventType.solarArcAspect.icon,
              score: 65.0, // 預設分數
              eventImportance: EventImportanceExtension.fromScore(65.0),
              involvedPlanets: [natalPlanet1.name, natalPlanet2.name],
              involvedHouses: [natalPlanet2.house, natalPlanet2.house],
              involvedSigns: [natalPlanet1.sign, natalPlanet2.sign],
              aspectType: aspectType,
              orb: orb,
              isExact: orb <= 0.5,
              additionalData: {
                'solarArcPlanet': natalPlanet1,
                'natalPlanet': natalPlanet2,
                'solarArcDegrees': solarArcDegrees,
                'isSolarArc': true,
                'event_category': eventCategory, // 添加事件分類
              },
            );

            events.add(event);
          }
        }
      }
      
      return events;
    } catch (e) {
      logger.e('偵測太陽弧推運事件時出錯: $e');
      return [];
    }
  }

  /// 偵測行星換座事件
  Future<List<AstroEvent>> _detectSignChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換座偵測邏輯
    return [];
  }

  /// 偵測行星換宮事件
  Future<List<AstroEvent>> _detectHouseChangeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    // TODO: 實作行星換宮偵測邏輯
    return [];
  }

  /// 檢查相位是否在指定日期活躍
  bool _isAspectActiveOnDate(dynamic aspect, DateTime date) {
    // 簡化實作：檢查容許度是否在合理範圍內
    if (aspect is Map) {
      return (aspect['orb'] as double) <= 3.0;
    }
    return false;
  }

  /// 生成事件ID
  String _generateEventId(String type, dynamic aspect, DateTime date) {
    final dateStr = date.toIso8601String().substring(0, 10);
    String aspectStr;

    if (aspect is Map) {
      final planet1Name = aspect['planet1']['name'] as String;
      final planet2Name = aspect['planet2']['name'] as String;
      final aspectType = aspect['aspect'] as String;
      aspectStr = '${planet1Name}_${aspectType}_${planet2Name}';
    } else {
      aspectStr = 'unknown_aspect';
    }

    return '${type}_${dateStr}_${aspectStr}';
  }

  /// 分數轉換為重要性等級
  int _scoreToImportance(double score) {
    if (score >= 90) return 5;
    if (score >= 70) return 4;
    if (score >= 50) return 3;
    if (score >= 30) return 2;
    return 1;
  }

  /// 獲取相位顏色
  Color _getAspectColor(String aspectName) {
    switch (aspectName) {
      case '合相':
        return Colors.red;
      case '對沖':
        return Colors.blue;
      case '四分相':
        return Colors.orange;
      case '三分相':
        return Colors.green;
      case '六分相':
        return Colors.purple;
      default:
        return AstroEventType.transitAspect.defaultColor;
    }
  }

  /// 偵測人生重大事件
  Future<List<AstroEvent>> _detectMajorLifeEvents(
    BirthData birthData,
    DateTime date,
  ) async {
    return await LifeEventsDetector.detectMajorLifeEvents(birthData, date);
  }
}
