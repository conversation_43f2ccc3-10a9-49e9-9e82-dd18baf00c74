# 快取有效期設定實作說明

## 📋 功能概述

實作了事件偵測快取的有效期設定功能，允許用戶自訂快取資料的有效期限，並修復了快取統計中的類型轉換錯誤。

## 🎯 主要功能

### 1. 動態快取有效期設定
- **可調整範圍**：1-30天
- **預設值**：7天
- **即時生效**：設定變更後立即清理過期快取
- **持久化儲存**：設定保存在 SharedPreferences 中

### 2. 快取有效期管理
- **自動載入**：應用啟動時自動載入用戶設定
- **動態更新**：所有快取操作都使用當前設定的有效期
- **智能清理**：設定變更時自動清理已過期的快取

### 3. 用戶介面優化
- **滑桿控制**：直觀的滑桿介面調整有效期
- **即時預覽**：顯示當前選擇的天數
- **說明文字**：提供設定建議和影響說明

## 🏗️ 技術實作

### EventCacheService 擴展

#### 新增的靜態變數
```dart
static const String _cacheExpiryKey = 'event_cache_expiry_days';
static Duration _cacheExpiry = const Duration(days: 7); // 預設快取7天
```

#### 新增的方法
```dart
/// 從 SharedPreferences 載入快取有效期設定
static Future<void> _loadCacheExpiryFromPrefs() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    final days = prefs.getInt(_cacheExpiryKey) ?? 7;
    _cacheExpiry = Duration(days: days);
  } catch (e) {
    logger.e('載入快取有效期設定失敗: $e');
    _cacheExpiry = const Duration(days: 7);
  }
}

/// 獲取當前快取有效期
static Future<Duration> getCurrentCacheExpiry() async {
  await _loadCacheExpiryFromPrefs();
  return _cacheExpiry;
}

/// 更新快取有效期
static Future<void> updateCacheExpiry(Duration newExpiry) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_cacheExpiryKey, newExpiry.inDays);
    _cacheExpiry = newExpiry;
    
    logger.i('快取有效期已更新為 ${newExpiry.inDays} 天');
    
    // 清理已過期的快取
    await _cleanupExpiredCache();
  } catch (e) {
    logger.e('更新快取有效期失敗: $e');
  }
}
```

### EventDetectionSettingsDialog 擴展

#### 新增的狀態變數
```dart
int _cacheExpiryDays = 7; // 預設7天
```

#### 新增的方法
```dart
/// 載入快取有效期設定
Future<void> _loadCacheExpirySettings() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    final expiryDays = prefs.getInt('event_cache_expiry_days') ?? 7;
    if (mounted) {
      setState(() {
        _cacheExpiryDays = expiryDays;
      });
    }
  } catch (e) {
    logger.e('載入快取有效期設定失敗: $e');
  }
}

/// 保存快取有效期設定
Future<void> _saveCacheExpirySettings(int days) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('event_cache_expiry_days', days);
    
    // 更新 EventCacheService 的有效期設定
    await EventCacheService.updateCacheExpiry(Duration(days: days));
    
    if (mounted) {
      setState(() {
        _cacheExpiryDays = days;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('快取有效期已設定為 $days 天'),
          backgroundColor: Colors.green,
        ),
      );
      
      // 觸發設定變更回調
      widget.onSettingsChanged?.call();
    }
  } catch (e) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('保存設定失敗: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

/// 顯示快取有效期設定對話框
Future<void> _showCacheExpiryDialog() async {
  int selectedDays = _cacheExpiryDays;
  
  final result = await showDialog<int>(
    context: context,
    builder: (context) => StatefulBuilder(
      builder: (context, setDialogState) => AlertDialog(
        title: const Text('設定快取有效期'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('選擇快取資料的有效期限：'),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('有效期：'),
                const SizedBox(width: 8),
                Expanded(
                  child: Slider(
                    value: selectedDays.toDouble(),
                    min: 1,
                    max: 30,
                    divisions: 29,
                    label: '$selectedDays 天',
                    onChanged: (value) {
                      setDialogState(() {
                        selectedDays = value.round();
                      });
                    },
                  ),
                ),
                Text('$selectedDays 天'),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '較短的有效期會確保資料更新，但會增加計算次數。\n較長的有效期可以提升效能，但資料可能不是最新的。',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(selectedDays),
            child: const Text('確定'),
          ),
        ],
      ),
    ),
  );
  
  if (result != null && result != _cacheExpiryDays) {
    await _saveCacheExpirySettings(result);
  }
}
```

## 🔧 修復的問題

### 1. 類型轉換錯誤
- **問題**：`type 'int' is not a subtype of type 'String?' in type cast`
- **原因**：快取統計中的類型處理不當
- **解決方案**：確保所有快取操作都正確載入設定並使用正確的類型

### 2. 靜態快取有效期
- **問題**：原本使用固定的7天快取有效期
- **解決方案**：改為動態載入用戶設定的有效期

### 3. 設定同步問題
- **問題**：快取操作可能使用舊的有效期設定
- **解決方案**：在每次快取操作前都載入最新設定

## 🎨 UI 設計特色

### 1. 設定對話框
- **滑桿控制**：1-30天範圍的滑桿
- **即時回饋**：顯示當前選擇的天數
- **說明文字**：解釋不同設定的影響

### 2. 視覺回饋
- **成功提示**：設定保存成功時顯示綠色提示
- **錯誤處理**：設定失敗時顯示紅色錯誤訊息
- **載入狀態**：適當的載入指示器

### 3. 用戶體驗
- **直觀操作**：點擊設定項目即可調整
- **即時生效**：設定變更後立即生效
- **智能建議**：提供設定建議和說明

## 📊 效能影響

### 1. 較短有效期（1-3天）
- **優點**：資料更新及時
- **缺點**：計算次數增加，可能影響效能

### 2. 中等有效期（4-14天）
- **優點**：平衡效能和資料新鮮度
- **缺點**：適中的計算負擔

### 3. 較長有效期（15-30天）
- **優點**：最佳效能，減少計算次數
- **缺點**：資料可能不是最新的

## 🔮 未來擴展

### 1. 智能有效期
- **自動調整**：根據使用頻率自動調整有效期
- **分類設定**：為不同類型的事件設定不同有效期
- **效能監控**：根據系統效能動態調整

### 2. 進階設定
- **快取大小限制**：設定最大快取大小
- **清理策略**：LRU、LFU等不同清理策略
- **壓縮選項**：快取資料壓縮設定

### 3. 統計增強
- **命中率統計**：顯示快取命中率
- **效能指標**：載入時間、節省的計算時間
- **使用分析**：快取使用模式分析

## 📝 使用說明

### 設定快取有效期
1. 在事件偵測設定對話框中點擊「快取有效期」
2. 使用滑桿調整期望的有效期（1-30天）
3. 點擊「確定」保存設定
4. 系統會自動清理已過期的快取

### 建議設定
- **頻繁使用**：建議設定較短有效期（3-7天）
- **偶爾使用**：建議設定中等有效期（7-14天）
- **效能優先**：建議設定較長有效期（14-30天）

---

*快取有效期設定功能提供了靈活的快取管理選項，讓用戶可以根據自己的需求平衡效能和資料新鮮度。*
