# 事件類型切換與最低分數設定實作說明

## 📋 功能概述

為事件偵測設定對話框實作了完整的事件類型切換邏輯和最低分數設定功能，讓用戶可以自訂要偵測的事件類型和過濾條件。

## 🎯 主要功能

### 1. 事件類型切換邏輯
- **支援的事件類型**：
  - 行運相位 (AstroEventType.transitAspect)
  - 推運相位 (AstroEventType.progressionAspect)
  - 太陽弧推運 (AstroEventType.solarArcAspect)
  - 行星換座 (AstroEventType.planetSignChange)
  - 行星換宮 (AstroEventType.planetHouseChange)

- **互動式選擇**：使用 FilterChip 提供直觀的切換介面
- **即時回饋**：選擇變更時顯示 SnackBar 確認
- **持久化儲存**：設定會自動保存到 SharedPreferences

### 2. 最低分數設定
- **分數範圍**：0-100 分，預設 20 分
- **滑桿介面**：使用 Slider 提供直觀的分數選擇
- **即時預覽**：拖動時即時顯示分數值
- **說明文字**：提供設定建議和影響說明

## 🏗️ 技術實作

### 資料模型擴展

#### EventDetectionSettingsService
新增專門的設定服務類別來管理事件偵測設定：

```dart
class EventDetectionSettingsService {
  // 單例模式
  static EventDetectionSettingsService get instance;
  
  // 主要方法
  Future<Set<AstroEventType>> getEnabledEventTypes();
  Future<bool> setEnabledEventTypes(Set<AstroEventType> eventTypes);
  Future<bool> toggleEventType(AstroEventType eventType, bool enabled);
  Future<double> getMinimumEventScore();
  Future<bool> setMinimumEventScore(double score);
  Future<EventDetectionConfig> getCurrentConfig();
}
```

#### UserPreferences 擴展
新增事件偵測相關的偏好設定常數和方法：

```dart
class UserPreferences {
  // 新增的設定鍵值
  static const String _eventMinimumScoreKey = 'event_minimum_score';
  static const String _eventEnabledTypesKey = 'event_enabled_types';
  static const String _eventCacheExpiryDaysKey = 'event_cache_expiry_days';
  
  // 新增的方法
  static Future<bool> saveEventMinimumScore(double score);
  static Future<double> getEventMinimumScore();
  static Future<bool> saveEventEnabledTypes(String enabledTypesJson);
  static Future<String?> getEventEnabledTypes();
}
```

### UI 組件更新

#### EventDetectionSettingsDialog 增強
```dart
class _EventDetectionSettingsDialogState extends State<EventDetectionSettingsDialog> {
  // 新增狀態變數
  double _minimumEventScore = 20.0;
  Set<AstroEventType> _enabledEventTypes = {};
  
  // 新增方法
  Future<void> _loadEventDetectionSettings();
  Future<void> _saveEventDetectionSettings();
  Future<void> _toggleEventType(AstroEventType eventType, bool enabled);
  Future<void> _showMinimumScoreDialog();
}
```

#### 事件類型晶片重構
```dart
Widget _buildEventTypeChip(String label, IconData icon, AstroEventType eventType) {
  final isEnabled = _enabledEventTypes.contains(eventType);
  
  return FilterChip(
    label: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16),
        const SizedBox(width: 4),
        Text(label),
      ],
    ),
    selected: isEnabled,
    onSelected: (selected) {
      _toggleEventType(eventType, selected);
    },
  );
}
```

#### 最低分數設定對話框
```dart
Future<void> _showMinimumScoreDialog() async {
  double selectedScore = _minimumEventScore;

  final result = await showDialog<double>(
    context: context,
    builder: (context) => StatefulBuilder(
      builder: (context, setDialogState) => AlertDialog(
        title: const Text('設定最低事件分數'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('只顯示分數高於此閾值的事件：'),
            Slider(
              value: selectedScore,
              min: 0,
              max: 100,
              divisions: 20,
              label: '${selectedScore.toInt()} 分',
              onChanged: (value) {
                setDialogState(() {
                  selectedScore = value;
                });
              },
            ),
            // 說明文字...
          ],
        ),
        // 按鈕...
      ),
    ),
  );
  
  // 處理結果...
}
```

## 🔧 技術修復

### IconData Tree Shaking 問題修復
修復了 AstroEvent.fromMap 中的 IconData 建構函數問題：

**問題**：
```dart
// 錯誤：非常數 IconData 建構函數
icon: IconData(map['icon'] as int, fontFamily: 'MaterialIcons'),
```

**修復**：
```dart
// 正確：使用預定義常數圖標
icon: _getIconFromCodePoint(map['icon'] as int),

static IconData _getIconFromCodePoint(int codePoint) {
  switch (codePoint) {
    case 0xe3a7: return Icons.wb_sunny;
    case 0xe3a8: return Icons.brightness_3;
    case 0xe8d2: return Icons.sync;
    // ... 更多映射
    default: return Icons.star;
  }
}
```

這個修復確保了：
- ✅ 編譯時不會出現 tree shaking 錯誤
- ✅ 圖標字體可以正確優化（97.2% 大小減少）
- ✅ 應用可以正常建置 release 版本

## 📱 使用者體驗

### 設定流程
1. **開啟設定對話框**：點擊事件偵測頁面的設定按鈕
2. **選擇事件類型**：在「事件類型」區域點擊晶片切換
3. **調整最低分數**：點擊「最低事件分數」項目開啟滑桿對話框
4. **即時生效**：所有設定變更會立即保存並生效

### 視覺回饋
- **晶片狀態**：選中的事件類型會高亮顯示
- **分數顯示**：當前設定的分數會顯示在副標題中
- **操作確認**：每次變更都會顯示 SnackBar 確認
- **載入狀態**：設定載入時會顯示適當的載入指示

## 🔮 預設設定

### 事件類型預設值
```dart
// 預設啟用的事件類型
{
  AstroEventType.transitAspect,    // 行運相位
  AstroEventType.progressionAspect, // 推運相位
}
```

### 分數設定預設值
```dart
minimumEventScore: 20.0  // 預設最低分數 20 分
cacheExpiryDays: 7       // 預設快取有效期 7 天
```

## 🚀 效能優化

### 設定快取
- 使用 SharedPreferences 進行本地快取
- 避免重複的網路請求和計算
- 設定變更時觸發回調更新相關組件

### 記憶體管理
- 使用單例模式管理設定服務
- 適當的狀態管理避免記憶體洩漏
- 及時釋放不需要的資源

## 🧪 測試建議

### 功能測試
1. **事件類型切換**：測試各種事件類型的啟用/停用
2. **分數設定**：測試不同分數閾值的效果
3. **設定持久化**：重啟應用後檢查設定是否保持
4. **邊界條件**：測試極值（0分、100分）的處理

### 效能測試
1. **設定載入速度**：測試設定載入的響應時間
2. **記憶體使用**：監控設定服務的記憶體佔用
3. **快取效率**：驗證設定快取的有效性

## 📈 未來擴展

### 進階設定
- 支援更多事件類型（月相、逆行等）
- 自訂事件權重設定
- 地區化事件偵測設定

### 使用者介面
- 設定匯入/匯出功能
- 預設配置模板
- 設定建議系統

### 整合功能
- 與其他占星功能的設定整合
- 雲端設定同步
- 多用戶設定管理
