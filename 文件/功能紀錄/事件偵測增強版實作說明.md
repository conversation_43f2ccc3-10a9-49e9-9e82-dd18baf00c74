# 事件偵測增強版實作說明

## 📋 功能概述

對 `_detectDailyEvents` 方法進行了重大升級，實作了基於多重星盤分析的智能事件偵測系統，能夠更準確地預測重大人生事件的發生時間和類型。

## 🎯 核心改進

### 1. 多重星盤分析架構
- **本命盤分析**：判斷重大事件的潛在機率
- **行運盤分析**：偵測當前天體觸發
- **次限推運盤**：分析內在心理發展觸發
- **太陽弧推運盤**：預測重大人生轉折點

### 2. 七大生活領域分析
根據占星學理論，系統化分析七個主要生活領域：

| 事件類型 | 關鍵宮位 | 主要關注行星 | 常見觸發行星與相位 |
|---------|---------|-------------|------------------|
| 感情與人際關係 | 7宮、5宮、4宮 | 金星、月亮、火星 | 行運土星、天王星、冥王星刑沖金星/月亮 |
| 事業與工作轉折 | 10宮、6宮 | 太陽、木星、土星 | 行運土星與MC刑沖；木星助力事業宮 |
| 財務狀況 | 2宮、8宮 | 金星、木星、土星 | 土星刑剋財務宮、木星擴張財務福氣 |
| 健康與身體狀況 | 6宮、8宮 | 火星、土星、天王星、冥王星 | 火星沖刑本命敏感點；天王星突發事故 |
| 學習與成長 | 3宮、9宮 | 水星、木星、天王星 | 木星帶來學習機會，天王星刺激創新突破 |
| 搬遷與環境變動 | 4宮、9宮 | 天王星、木星 | 天王星刺激搬遷突發，木星擴張旅行機會 |
| 心靈與命運轉折 | 8宮、12宮 | 冥王星、海王星、土星 | 冥王星深層轉化，海王星靈性覺醒 |

## 🏗️ 技術架構

### 第一階段：多重星盤計算
```dart
Future<Map<ChartType, ChartData>> _calculateMultipleCharts(
  BirthData birthData,
  DateTime date,
) async {
  // 計算本命盤、行運盤、次限推運盤、太陽弧推運盤
  // 使用 AstrologyService.calculateChartData() 方法
}
```

### 第二階段：本命盤敏感度分析
```dart
Future<Map<String, dynamic>> _analyzeNatalChartForEventPotential(
  ChartData natalChart,
  BirthData birthData,
) async {
  // 分析七大生活領域的敏感度
  // 檢查關鍵宮位的行星配置
  // 分析重要行星的相位模式
}
```

### 第三階段：推運觸發分析
```dart
Future<Map<String, dynamic>> _analyzeProgressionChartsForTiming(
  Map<ChartType, ChartData> chartDataMap,
  BirthData birthData,
  DateTime date,
  Map<String, dynamic> natalAnalysis,
) async {
  // 分析行運觸發
  // 分析推運觸發
  // 分析太陽弧推運觸發
}
```

### 第四階段：事件綜合生成
```dart
Future<List<AstroEvent>> _synthesizeEventsFromAnalysis(
  Map<ChartType, ChartData> chartDataMap,
  Map<String, dynamic> natalAnalysis,
  Map<String, dynamic> progressionAnalysis,
  BirthData birthData,
  DateTime date,
) async {
  // 綜合所有分析結果
  // 生成具體的事件預測
  // 計算事件分數和重要性
}
```

## 🔍 敏感度分析方法

### 1. 感情與人際關係敏感度
- **檢查宮位**：7宮（伴侶）、5宮（戀愛）、4宮（家庭）
- **關鍵行星**：金星（愛情）、月亮（情感）、火星（激情）
- **相位分析**：檢查關鍵行星的挑戰性和和諧性相位

### 2. 事業與工作轉折敏感度
- **檢查宮位**：10宮（事業）、6宮（工作）
- **關鍵行星**：太陽（自我實現）、木星（機會）、土星（責任）
- **觸發條件**：行運土星與MC的相位、木星對事業宮的影響

### 3. 財務狀況敏感度
- **檢查宮位**：2宮（個人財務）、8宮（共同財務）
- **關鍵行星**：金星（價值）、木星（擴張）、土星（限制）
- **分析重點**：財務宮位的行星配置和相位模式

### 4. 健康與身體狀況敏感度
- **檢查宮位**：6宮（健康）、8宮（生死）
- **關鍵行星**：火星（急性）、土星（慢性）、天王星（突發）、冥王星（轉化）
- **風險評估**：檢查健康相關行星的挑戰性相位

### 5. 學習與成長敏感度
- **檢查宮位**：3宮（學習）、9宮（高等教育）
- **關鍵行星**：水星（思維）、木星（智慧）、天王星（創新）
- **機會識別**：學習宮位的有利行星配置

### 6. 搬遷與環境變動敏感度
- **檢查宮位**：4宮（居住）、9宮（遠行）
- **關鍵行星**：天王星（變動）、木星（擴張）
- **變動指標**：居住相關宮位的行星活動

### 7. 心靈與命運轉折敏感度
- **檢查宮位**：8宮（轉化）、12宮（靈性）
- **關鍵行星**：冥王星（深層轉化）、海王星（靈性覺醒）、土星（考驗）
- **轉化徵象**：深層轉化行星的重要相位

## 🎯 觸發分析系統

### 行運觸發分析
- **容許度**：3度（較寬鬆，適合行運的快速變化）
- **重點相位**：合相、對沖、四分相、三分相、六分相
- **分析方法**：行運行星與本命行星的精確相位

### 推運觸發分析
- **容許度**：1度（嚴格，適合推運的緩慢變化）
- **重點關注**：推運行星與本命行星的精確相位
- **時間意義**：反映內在心理發展的重要時刻

### 太陽弧推運觸發分析
- **容許度**：1度（非常嚴格）
- **特殊意義**：預測重大人生轉折點
- **計算方法**：所有行星按太陽推進速度同步移動

## 🔄 事件生成流程

### 1. 觸發器評估
- 計算相位強度（基於相位類型和容許度）
- 評估事件潛力（結合本命敏感度分析）
- 確定主要事件類型（七大生活領域）

### 2. 事件創建
- 生成事件標題和描述
- 計算事件分數和重要性等級
- 設定事件顏色和圖標
- 記錄涉及的行星和相位資訊

### 3. 品質控制
- 過濾低分事件（低於最小閾值）
- 避免重複事件
- 確保事件的占星學合理性

## 🛡️ 錯誤處理與回退機制

### 多層次錯誤處理
1. **增強分析失敗**：自動回退到傳統事件偵測方法
2. **星盤計算失敗**：記錄錯誤並使用預設值
3. **敏感度分析失敗**：使用平均敏感度值（50.0）

### 向後相容性
- 保留原始的傳統事件偵測方法
- 確保現有功能不受影響
- 漸進式升級，可以選擇性啟用增強功能

## 📊 效能優化

### 快取策略
- 本命盤分析結果可以快取（不常變化）
- 推運分析結果按日期快取
- 星盤計算結果利用現有快取系統

### 計算優化
- 只計算必要的星盤類型
- 並行處理多個敏感度分析
- 早期過濾低分觸發器

## 🔮 未來擴展

### 1. 機器學習整合
- 基於歷史事件資料訓練模型
- 個人化事件預測準確度
- 動態調整敏感度權重

### 2. 更多星盤類型
- 太陽返照盤分析
- 月亮返照盤分析
- 日月蝕盤影響

### 3. 進階相位分析
- 複合相位模式
- 中點理論應用
- 阿拉伯點觸發

## 📝 使用建議

### 配置建議
- 設定適當的最小事件分數閾值（建議20-30分）
- 啟用所有事件類型以獲得完整分析
- 定期清理快取以確保資料新鮮度

### 解讀建議
- 重點關注高分事件（70分以上）
- 結合多個事件類型進行綜合判斷
- 注意事件的時間精確度（容許度）

---

*事件偵測增強版提供了更科學、更準確的占星事件預測能力，結合傳統占星學理論與現代計算技術，為用戶提供深度的人生指導。*
