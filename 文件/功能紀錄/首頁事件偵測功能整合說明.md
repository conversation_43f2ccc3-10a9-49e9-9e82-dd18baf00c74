# 首頁事件偵測功能整合說明

## 📋 功能概述

已成功在初心者首頁（StarlightHomePage）和占星師首頁（StarmasterHomePage）新增事件偵測功能入口，讓用戶可以直接從首頁進入事件偵測分析。

## 🎯 實作內容

### 1. Starlight 初心者首頁整合

#### 新增位置
- 在「每日星象」區域下方新增「事件偵測」卡片
- 位於每日星相卡片和問事占星卡片之間

#### 卡片設計
```dart
UnifiedFeatureCard(
  title: '事件偵測',
  subtitle: '分析未來重要占星事件，掌握人生關鍵時機',
  icon: Icons.timeline,
  color: const Color(0xFF6200EA), // 紫色主題
  onTap: () => _navigateToEventDetection(viewModel),
)
```

#### 特色說明
- **標題**：「事件偵測」- 簡潔易懂
- **副標題**：強調「人生關鍵時機」，符合初心者關注點
- **圖標**：使用 `timeline` 圖標，直觀表達時間軸概念
- **顏色**：紫色 (#6200EA)，與其他功能區分

### 2. Starmaster 占星師首頁整合

#### 新增位置
- 在「每日星象」區域下方新增「事件偵測分析」卡片
- 位於每日星相卡片和時事占星工具區域之間

#### 卡片設計
```dart
UnifiedFeatureCard(
  title: '事件偵測分析',
  subtitle: '專業占星事件偵測，精準掌握時機變化',
  icon: Icons.timeline,
  color: const Color(0xFF6200EA), // 紫色主題
  onTap: () => _navigateToEventDetection(viewModel),
)
```

#### 特色說明
- **標題**：「事件偵測分析」- 強調專業性
- **副標題**：突出「專業」和「精準」，符合占星師需求
- **圖標**：同樣使用 `timeline` 圖標保持一致性
- **顏色**：與初心者模式使用相同紫色，建立品牌一致性

## 🔧 技術實作

### 1. 導入依賴
兩個首頁都新增了事件偵測頁面的導入：
```dart
import '../astrology/astro_event_detection_page.dart';
```

### 2. 導航方法實作

#### Starlight 版本
```dart
/// 導航到事件偵測頁面
void _navigateToEventDetection(HomeViewModel homeViewModel) {
  final selectedPerson = homeViewModel.selectedPerson;
  
  if (selectedPerson == null) {
    // 如果沒有選中的人物，提示用戶先選擇
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('請先選擇一個人物來進行事件偵測'),
        backgroundColor: Colors.orange,
      ),
    );
    return;
  }

  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => AstroEventDetectionPage(
        birthData: selectedPerson,
      ),
    ),
  );
}
```

#### Starmaster 版本
```dart
/// 導航到事件偵測頁面
void _navigateToEventDetection(HomeViewModel homeViewModel) {
  final selectedPerson = homeViewModel.selectedPerson;
  
  if (selectedPerson == null) {
    // 如果沒有選中的人物，提示用戶先選擇
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('請先選擇一個人物來進行專業事件偵測分析'),
        backgroundColor: Colors.orange,
      ),
    );
    return;
  }

  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => AstroEventDetectionPage(
        birthData: selectedPerson,
      ),
    ),
  );
}
```

### 3. 用戶體驗設計

#### 智能提示系統
- **檢查選中人物**：在進入事件偵測前檢查是否有選中的人物
- **友善提示**：如果沒有選中人物，顯示橙色 SnackBar 提示
- **差異化訊息**：
  - 初心者模式：「請先選擇一個人物來進行事件偵測」
  - 占星師模式：「請先選擇一個人物來進行專業事件偵測分析」

#### 無縫整合
- **利用現有架構**：使用 HomeViewModel 的 selectedPerson 屬性
- **保持一致性**：使用相同的 UnifiedFeatureCard 組件
- **主題適配**：自動適應 Starmaster/Starlight 主題

## 🎨 UI設計考量

### 1. 視覺層次
- **位置安排**：放在每日星象下方，強調時間相關性
- **顏色選擇**：紫色 (#6200EA) 與其他功能區分，突出重要性
- **間距設計**：使用標準 12px 間距保持視覺平衡

### 2. 文案設計
- **初心者友善**：使用「人生關鍵時機」等生活化語言
- **專業導向**：占星師版本強調「專業」和「精準」
- **行動導向**：副標題都包含行動詞彙（「掌握」、「分析」）

### 3. 圖標選擇
- **timeline 圖標**：直觀表達時間軸和事件序列概念
- **一致性**：兩個模式使用相同圖標建立認知一致性
- **語義清晰**：圖標與功能高度相關

## 🚀 使用流程

### 1. 用戶操作流程
1. **進入首頁**：用戶打開 Starlight 或 Starmaster 首頁
2. **選擇人物**：通過快速開始或人物選擇器選擇要分析的人物
3. **點擊事件偵測**：點擊「事件偵測」或「事件偵測分析」卡片
4. **進入分析**：自動帶入選中人物的出生資料進入事件偵測頁面

### 2. 錯誤處理流程
1. **未選擇人物**：點擊事件偵測卡片
2. **顯示提示**：橙色 SnackBar 提示需要先選擇人物
3. **引導操作**：用戶返回選擇人物後再次嘗試

## 📱 響應式設計

### 1. 卡片適配
- **自動寬度**：UnifiedFeatureCard 自動適應螢幕寬度
- **觸摸友善**：足夠的點擊區域和視覺回饋
- **文字適配**：標題和副標題自動換行

### 2. 主題適配
- **自動主題**：根據 Starmaster/Starlight 模式自動適配
- **顏色一致**：紫色主題在兩種模式下都保持良好對比度
- **深色模式**：支援深色模式下的顯示效果

## 🔮 未來擴展

### 1. 功能增強
- **快捷預設**：可以新增常用的事件偵測預設（如年度分析、月度分析）
- **智能推薦**：根據當前日期推薦最適合的分析時間範圍
- **批量分析**：支援對多個人物進行批量事件偵測

### 2. UI優化
- **動畫效果**：新增卡片點擊動畫和頁面過場效果
- **個性化**：允許用戶自訂事件偵測卡片的顯示位置
- **統計顯示**：在卡片上顯示最近的重要事件預覽

### 3. 整合優化
- **深度整合**：與每日星象功能聯動，顯示相關事件
- **智能提醒**：結合通知系統，提醒重要事件即將發生
- **分享功能**：支援將事件分析結果分享到社交媒體

## 📝 總結

事件偵測功能已成功整合到兩個首頁中，提供了：

1. **無縫體驗**：從首頁直接進入，自動帶入選中人物資料
2. **智能提示**：友善的錯誤處理和用戶引導
3. **差異化設計**：針對不同用戶群體的個性化文案
4. **視覺一致性**：保持與現有設計風格的一致性
5. **擴展性**：為未來功能增強預留了空間

這個整合為用戶提供了便捷的事件偵測入口，大大提升了功能的可發現性和使用便利性。
