# 事件偵測設定對話框實作說明

## 📋 功能概述

為占星事件偵測頁面實作了完整的設定對話框，包含快取管理、事件類型設定和效能配置等功能。

## 🎯 主要功能

### 1. 快取管理
- **快取統計顯示**：顯示總項目數、大小、有效項目、過期項目
- **清除此用戶快取**：清除當前用戶的所有事件偵測快取
- **清除所有快取**：清除所有用戶的快取（危險操作）
- **確認對話框**：提供安全的確認機制

### 2. 事件類型設定
- **事件類型選擇**：可選擇要偵測的事件類型
- **視覺化晶片**：使用 FilterChip 提供直觀的選擇介面
- **即時回饋**：選擇變更時提供即時回饋

### 3. 效能設定
- **快取有效期**：設定快取的有效期限
- **最低事件分數**：設定顯示事件的最低分數閾值
- **設定項目**：使用 ListTile 提供清晰的設定介面

## 🏗️ 技術實作

### 設定對話框結構

```dart
class _EventDetectionSettingsDialog extends StatefulWidget {
  final BirthData birthData;
  final VoidCallback? onSettingsChanged;
  
  // 構造函數和狀態管理
}
```

### 快取管理功能

#### 快取統計顯示
```dart
Widget _buildCacheStatsCard() {
  final stats = _cacheStats!;
  final totalItems = stats['totalItems'] as int;
  final totalSizeKB = (stats['totalSizeBytes'] as int) / 1024;
  final validItems = stats['validItems'] as int;
  final expiredItems = stats['expiredItems'] as int;
  
  return Card(
    child: Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          Text('快取統計'),
          Row(children: [
            Text('總項目數: $totalItems'),
            Text('大小: ${totalSizeKB.toStringAsFixed(1)} KB'),
          ]),
          Row(children: [
            Text('有效項目: $validItems'),
            Text('過期項目: $expiredItems'),
          ]),
        ],
      ),
    ),
  );
}
```

#### 快取清除操作
```dart
Future<void> _clearCurrentUserCache() async {
  final confirmed = await _showConfirmDialog(
    '清除此用戶快取',
    '確定要清除此用戶的所有事件偵測快取嗎？...',
  );

  if (!confirmed) return;

  setState(() => _isLoading = true);

  try {
    await EventCacheService.clearCacheForBirthData(widget.birthData);
    await _loadCacheStats();
    // 顯示成功訊息
    widget.onSettingsChanged?.call();
  } catch (e) {
    // 錯誤處理
  } finally {
    setState(() => _isLoading = false);
  }
}
```

### 事件類型設定

```dart
Widget _buildEventTypeSection() {
  return Column(
    children: [
      Text('事件類型'),
      Wrap(
        children: [
          _buildEventTypeChip('行運相位', Icons.sync, true),
          _buildEventTypeChip('推運相位', Icons.trending_up, true),
          _buildEventTypeChip('太陽弧推運', Icons.wb_sunny, false),
          // ... 其他事件類型
        ],
      ),
    ],
  );
}

Widget _buildEventTypeChip(String label, IconData icon, bool isEnabled) {
  return FilterChip(
    label: Row(
      children: [
        Icon(icon, size: 16),
        Text(label),
      ],
    ),
    selected: isEnabled,
    onSelected: (selected) {
      // TODO: 實作事件類型切換邏輯
    },
  );
}
```

### 安全確認機制

```dart
Future<bool> _showConfirmDialog(String title, String content, {bool isDangerous = false}) async {
  final result = await showDialog<bool>(
    context: context,
    builder: (context) => AlertDialog(
      title: Row(
        children: [
          Icon(
            isDangerous ? Icons.warning : Icons.help_outline,
            color: isDangerous ? Colors.red : Colors.orange,
          ),
          Text(title),
        ],
      ),
      content: Text(content),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: isDangerous ? Colors.red : Colors.orange,
          ),
          child: const Text('確定'),
        ),
      ],
    ),
  );
  
  return result ?? false;
}
```

## 🎨 UI 設計特色

### 1. 分區設計
- **快取管理區域**：統計資訊 + 操作按鈕
- **事件類型區域**：可選擇的事件類型晶片
- **效能設定區域**：各種效能相關設定

### 2. 視覺回饋
- **載入狀態**：按鈕顯示載入指示器
- **顏色編碼**：橙色（警告）、紅色（危險）、綠色（成功）
- **圖標提示**：每個功能都有對應的圖標

### 3. 安全設計
- **確認對話框**：危險操作需要確認
- **差異化提示**：不同危險等級使用不同顏色和圖標
- **詳細說明**：清楚說明操作的後果

## 📊 快取統計資訊

### 顯示項目
- **總項目數**：快取中的總項目數量
- **總大小**：快取佔用的儲存空間（KB）
- **有效項目**：未過期的快取項目數量
- **過期項目**：已過期的快取項目數量

### 統計來源
```dart
Future<void> _loadCacheStats() async {
  try {
    final stats = await EventCacheService.getCacheStatistics();
    setState(() {
      _cacheStats = stats;
    });
  } catch (e) {
    // 錯誤處理
  }
}
```

## 🔧 快取操作功能

### 1. 清除此用戶快取
- **功能**：只清除當前用戶的快取資料
- **用途**：當用戶資料有變更時清除相關快取
- **安全性**：中等風險，需要確認

### 2. 清除所有快取
- **功能**：清除所有用戶的快取資料
- **用途**：系統維護或重大更新時使用
- **安全性**：高風險，需要特別確認

### 操作流程
1. 用戶點擊清除按鈕
2. 顯示確認對話框
3. 用戶確認後執行清除
4. 顯示載入狀態
5. 更新快取統計
6. 顯示操作結果
7. 觸發設定變更回調

## 🎯 事件類型管理

### 支援的事件類型
- **行運相位**：行運行星與本命行星的相位（預設啟用）
- **推運相位**：推運行星與本命行星的相位（預設啟用）
- **太陽弧推運**：太陽弧推運相位（預設停用）
- **行星換座**：行星進入新星座（預設停用）
- **行星換宮**：行星進入新宮位（預設停用）

### 選擇介面
- 使用 `FilterChip` 提供直觀的選擇介面
- 每個類型都有對應的圖標
- 即時顯示選擇狀態
- 支援多選組合

## ⚙️ 效能設定項目

### 1. 快取有效期
- **當前設定**：7 天
- **功能**：控制快取資料的有效期限
- **影響**：過期快取會自動清理

### 2. 最低事件分數
- **當前設定**：20 分
- **功能**：只顯示分數高於此閾值的事件
- **影響**：過濾低重要性事件

### 設定介面
- 使用 `ListTile` 提供清晰的設定項目
- 顯示當前設定值
- 點擊進入詳細設定（待實作）

## 🔮 未來擴展

### 1. 進階設定
- **自訂快取有效期**：允許用戶設定快取期限
- **分數閾值調整**：滑桿調整最低分數
- **事件類型權重**：為不同事件類型設定權重

### 2. 匯出功能
- **設定匯出**：匯出當前設定為檔案
- **設定匯入**：從檔案匯入設定
- **設定同步**：雲端同步設定

### 3. 統計增強
- **使用統計**：顯示功能使用統計
- **效能監控**：顯示載入時間等效能指標
- **快取命中率**：顯示快取的效果

## 📝 使用說明

### 開啟設定對話框
1. 在事件偵測頁面點擊右上角的設定按鈕
2. 設定對話框會彈出顯示

### 管理快取
1. 查看快取統計資訊
2. 根據需要選擇清除範圍
3. 確認清除操作
4. 等待操作完成

### 調整事件類型
1. 在事件類型區域查看可用類型
2. 點擊晶片切換啟用/停用狀態
3. 設定會即時生效

### 效能設定
1. 查看當前效能設定
2. 點擊設定項目進入詳細設定
3. 調整相關參數

---

*事件偵測設定對話框提供了完整的快取管理和設定功能，讓用戶可以靈活控制事件偵測的行為和效能。*
