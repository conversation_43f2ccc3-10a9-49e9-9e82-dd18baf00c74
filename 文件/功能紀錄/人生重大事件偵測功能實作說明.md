# 人生重大事件偵測功能實作說明

## 📋 功能概述

實作了完整的人生重大事件偵測功能，能夠偵測七大類人生重要轉折點，幫助用戶了解重要的人生時機。

## 🎯 七大類人生重大事件

### 1. 感情與人際關係
**關鍵宮位**：
- 7宮（伴侶宮）：婚姻、伴侶關係
- 5宮（愛情宮）：戀愛、浪漫關係
- 4宮（家庭宮）：家庭和諧、親情關係

**關鍵行星**：
- 金星：愛情、美感、和諧
- 月亮：情感、直覺、家庭
- 火星：激情、行動、衝突

**偵測事件**：
- 結婚、戀愛、分手、離婚
- 家庭變動（生子、親人健康、搬家）
- 人際關係重大變化

### 2. 事業與工作轉折
**關鍵宮位**：
- 10宮（事業宮）：事業發展、社會地位
- 6宮（工作宮）：工作環境、日常職務

**關鍵行星**：
- 太陽：權威、領導、成就
- 木星：擴展、機會、成長
- 土星：責任、限制、考驗

**偵測事件**：
- 換工作、升職、創業、事業低谷
- 重大專案、職場人際變動

### 3. 財務狀況
**關鍵宮位**：
- 2宮（財務宮）：個人收入、財務管理
- 8宮（資源宮）：投資理財、資源共享、債務

**關鍵行星**：
- 金星：價值觀、物質享受
- 木星：財富增長、投資機會
- 土星：財務責任、風險管理

**偵測事件**：
- 資產增加、投資成功或失敗
- 負債、經濟壓力

### 4. 健康與身體狀況
**關鍵宮位**：
- 6宮（健康宮）：日常健康、疾病預防
- 8宮（醫療宮）：醫療康復、身體變化

**關鍵行星**：
- 火星：體力、炎症、意外
- 土星：慢性疾病、骨骼
- 天王星：突發狀況、神經系統
- 冥王星：深層療癒、重大手術

**偵測事件**：
- 生病、手術、身體狀況變化
- 健康改善或意外傷害

### 5. 學習與成長
**關鍵宮位**：
- 3宮（學習宮）：基礎學習、交流溝通
- 9宮（高等教育宮）：哲學思考、高等教育

**關鍵行星**：
- 水星：學習能力、溝通技巧
- 木星：智慧擴展、高等教育
- 天王星：創新思維、突破性學習

**偵測事件**：
- 進修、學業轉變、技能習得
- 心靈與心理成長、轉念

### 6. 搬遷與環境變動
**關鍵宮位**：
- 4宮（家庭宮）：居住環境、家庭基礎
- 9宮（遠行宮）：遠距離移動、異地生活

**關鍵行星**：
- 天王星：突發變動、環境改變
- 木星：擴展視野、有利變動

**偵測事件**：
- 搬家、旅遊、移民
- 工作或生活環境重大調整

### 7. 心靈與命運轉折
**關鍵宮位**：
- 8宮（轉化宮）：死亡與重生、深層變化
- 12宮（潛意識宮）：靈性覺醒、隱藏力量

**關鍵行星**：
- 冥王星：深層轉化、重生
- 海王星：靈性覺醒、直覺
- 土星：業力課題、人生考驗

**偵測事件**：
- 生命大轉折、重大選擇、靈性覺醒
- 深刻危機與療癒

## 🏗️ 技術架構

### 檔案結構
```
lib/features/astrology/services/
├── event_detection_service.dart (主要偵測服務)
├── major_life_events_detector.dart (重大事件偵測器)
└── major_life_events_helpers.dart (輔助方法)
```

### 核心類別

#### EventDetectionService
- 主要的事件偵測服務
- 整合所有類型的事件偵測
- 提供統一的偵測介面

#### MajorLifeEventsDetector
- 專門處理人生重大事件偵測
- 包含健康、教育、搬遷、心靈事件的偵測邏輯
- 使用靜態方法提供偵測功能

#### MajorLifeEventsHelpers
- 提供所有輔助計算方法
- 包含相位計算、分數計算、標題生成等
- 支援所有七大類事件的輔助功能

## 🔧 偵測邏輯

### 相位計算
```dart
static Map<String, dynamic>? calculateAspect(double longitude1, double longitude2) {
  final angleDiff = (longitude1 - longitude2).abs();
  final normalizedAngle = angleDiff > 180 ? 360 - angleDiff : angleDiff;
  
  // 檢查主要相位
  if ((normalizedAngle - 0).abs() <= 8) {
    return {'type': '合相', 'orb': (normalizedAngle - 0).abs()};
  }
  // ... 其他相位
}
```

### 事件分數計算
每種事件類型都有專門的分數計算邏輯：

```dart
static double calculateRelationshipEventScore(
  String transitPlanet,
  String natalPlanet,
  String aspectType,
  int house,
) {
  double baseScore = 50.0;
  
  // 行星權重
  switch (transitPlanet) {
    case 'Venus': baseScore += 20; break;
    case 'Mars': baseScore += 15; break;
    case 'Moon': baseScore += 10; break;
  }
  
  // 相位權重
  switch (aspectType) {
    case '合相': baseScore += 25; break;
    case '對沖': baseScore += 20; break;
    // ... 其他相位
  }
  
  // 宮位權重
  switch (house) {
    case 7: baseScore += 25; break; // 伴侶宮
    case 5: baseScore += 20; break; // 愛情宮
    case 4: baseScore += 15; break; // 家庭宮
  }
  
  return baseScore;
}
```

### 事件生成
```dart
final event = AstroEvent(
  id: 'relationship_${date.millisecondsSinceEpoch}_${transitPlanet.name}_${natalPlanet.name}',
  title: getRelationshipEventTitle(transitPlanet.name, aspect['type'], natalPlanet.house),
  description: getRelationshipEventDescription(...),
  dateTime: date,
  type: AstroEventType.transitAspect,
  importance: scoreToImportance(score),
  score: score,
  additionalData: {
    'category': '感情與人際關係',
    'keyHouse': natalPlanet.house,
    'isRelationshipEvent': true,
  },
);
```

## 📊 分數系統

### 分數範圍
- **90+ 分**：極高重要性（5級）
- **75-89 分**：高重要性（4級）
- **60-74 分**：中等重要性（3級）
- **45-59 分**：低重要性（2級）
- **45分以下**：極低重要性（1級）

### 分數計算因素

#### 行星權重
不同行星對不同事件類型有不同的重要性：
- **感情事件**：金星(+20) > 火星(+15) > 月亮(+10)
- **事業事件**：太陽(+25) > 木星(+20) > 土星(+15)
- **財務事件**：木星(+25) > 金星(+20) > 土星(+15)

#### 相位權重
- **合相**：+25分（最強影響）
- **對沖**：+20分（強烈對立）
- **三分相**：+15分（和諧流動）
- **四分相**：+10分（挑戰機會）
- **六分相**：+5分（輕微影響）

#### 宮位權重
根據事件類型，相關宮位獲得額外分數：
- **主要宮位**：+25-30分
- **次要宮位**：+15-20分

## 🎨 視覺設計

### 事件顏色
每種事件類型都有專屬的顏色系統：
- **感情事件**：粉紅、紅色、橙色系
- **事業事件**：琥珀、藍色、棕色系
- **財務事件**：綠色、青色、灰色系
- **健康事件**：紅色、橙色、紫色系
- **學習事件**：藍色、紫色、青色系
- **搬遷事件**：橙色、藍色系
- **心靈事件**：紫色、深藍、黑色系

### 事件圖標
- **感情**：❤️ 💕 🏠 👥
- **事業**：💼 📈 🏢
- **財務**：💰 💳 📊
- **健康**：🏥 ⚕️ 💊
- **學習**：📚 🎓 💡
- **搬遷**：🏠 ✈️ 🌍
- **心靈**：🔮 🧘 ⭐

## 🔮 使用方式

### 整合到主偵測服務
```dart
// 在 _detectDailyEvents 方法中
final majorLifeEvents = await _detectMajorLifeEvents(birthData, date);
events.addAll(majorLifeEvents);
```

### 偵測特定類型事件
```dart
// 偵測感情事件
final relationshipEvents = await _detectRelationshipEvents(natalPlanets, transitPlanets, date);

// 偵測事業事件
final careerEvents = await _detectCareerEvents(natalPlanets, transitPlanets, date);

// 偵測健康事件
final healthEvents = await MajorLifeEventsDetector.detectHealthEvents(natalPlanets, transitPlanets, date);
```

## 📝 事件描述範例

### 感情事件
- **標題**：「金星合相 - 伴侶關係轉機」
- **描述**：「行運金星與本命太陽形成合相，可能影響您的伴侶關係和婚姻狀況。這是一個重要的感情轉折期，建議關注相關領域的變化。」

### 事業事件
- **標題**：「木星三分相 - 事業發展擴展」
- **描述**：「行運木星與本命火星形成三分相，預示著事業發展和社會地位的重要轉折。這可能是升職、轉職或事業突破的關鍵時期。」

### 財務事件
- **標題**：「土星四分相 - 財務狀況考驗」
- **描述**：「行運土星與本命金星形成四分相，可能影響您的個人收入和財務管理。建議關注財務規劃和投資決策。」

## 🚀 後續擴展

### 1. 更精確的時間預測
- 實作精確到小時的事件時間預測
- 考慮月亮的快速移動對事件觸發的影響
- 整合推運和太陽返照盤的分析

### 2. 個人化權重系統
- 根據個人星盤特色調整權重
- 考慮本命盤的強弱行星
- 整合個人的人生階段和年齡因素

### 3. 複合事件分析
- 偵測多個事件同時發生的複合影響
- 分析事件之間的相互作用
- 提供更全面的人生轉折預測

### 4. 歷史驗證功能
- 回顧過去的重大事件預測準確性
- 提供個人化的預測調整
- 建立個人的事件模式資料庫

---

*人生重大事件偵測功能為用戶提供了深入的人生轉折點分析，幫助用戶更好地理解和準備人生的重要時刻。*
