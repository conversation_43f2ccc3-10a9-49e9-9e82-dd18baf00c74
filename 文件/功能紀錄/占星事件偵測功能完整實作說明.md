# 占星事件偵測功能完整實作說明

## 📋 功能概述

占星事件偵測功能是一個完整的占星分析工具，能夠偵測和分析各種占星事件，並以視覺化的方式呈現給用戶。

### 🎯 核心功能

1. **年曆熱度圖**：使用顏色強度顯示每日事件分數
2. **時間軸圖表**：展示事件分數的變化趨勢
3. **事件詳情面板**：點擊日期展開顯示詳細的事件資訊
4. **多主題支援**：支援Starmaster/Starlight模式和自訂配色

## 🏗️ 系統架構

### 資料模型層 (Models)

#### 1. AstroEvent (擴展現有模型)
- 新增事件偵測相關欄位：`score`、`eventImportance`、`involvedPlanets`等
- 支援新的事件類型：行運相位、推運相位、太陽弧推運等
- 新增 `EventImportance` 枚舉和相關擴展方法

#### 2. EventScore
- `EventScore`：單個事件的評分資訊
- `DailyEventScore`：每日事件評分匯總
- `EventScoreConfig`：評分配置參數

#### 3. EventDetectionConfig
- 事件偵測的完整配置系統
- 支援推運設定、相位設定、行星設定、宮位設定
- 包含 `ProgressionSettings`、`AspectSettings`、`PlanetSettings`、`HouseSettings`

#### 4. EventTimelineData
- 時間軸資料的完整模型
- 包含每日評分、統計資訊、月度摘要
- 支援日期範圍查詢和事件篩選

### 計算引擎層 (Calculation Engine)

#### 1. EventScoreCalculator
- 實作複雜的事件評分算法
- 結合行星權重、相位強度、宮位重要性、時間精確度、個人化影響
- 支援相位事件、行星換座、行星換宮等不同類型事件的評分

#### 2. EventDetectionService
- 核心事件偵測服務
- 支援行運、次限推運、太陽弧推運等多種推運技法
- 逐日偵測事件並生成時間軸資料
- 整合評分計算和事件篩選

### UI組件層 (UI Components)

#### 1. AstroEventCalendarWidget
- 年曆熱度圖組件
- 使用 `table_calendar` 套件實作
- 支援多主題配色方案 (`EventCalendarTheme`)
- 互動式日期選擇和事件顯示

#### 2. EventTimelineWidget
- 時間軸圖表組件
- 使用 `fl_chart` 套件實作
- 支援縮放、互動操作和工具提示
- 多主題配色支援 (`EventTimelineTheme`)

#### 3. EventDetailPanel
- 事件詳情面板組件
- 動畫展開/收合效果
- 詳細的事件資訊展示
- 評分詳情和標籤系統

### 頁面整合層 (Page Integration)

#### AstroEventDetectionPage
- 主要整合頁面
- 支援 TabBar 切換不同視圖
- 整合所有UI組件
- 完整的載入狀態和錯誤處理

## 🎨 主題系統

### 多主題支援

1. **Starmaster 專業模式**
   - 使用藍色系配色
   - 專業、簡潔的設計風格

2. **Starlight 初心者模式**
   - 使用琥珀色系配色
   - 溫暖、友善的設計風格

3. **深色模式**
   - 暗色背景配色
   - 適合夜間使用

### 主題類別

- `EventCalendarTheme`：年曆熱度圖主題
- `EventTimelineTheme`：時間軸圖表主題
- `EventDetailTheme`：事件詳情面板主題

## 📊 事件評分算法

### 評分公式

```
事件分數 = (行星權重 × 相位強度 × 宮位重要性 × 時間精確度 × 個人化影響) × 100
```

### 評分因子

1. **行星權重**
   - 太陽/月亮：1.0
   - 個人行星（水金火）：0.8
   - 社會行星（木土）：0.6
   - 外行星（天海冥）：0.4

2. **相位強度**
   - 合相/對沖：1.0
   - 四分相/三分相：0.8
   - 六分相：0.6
   - 次要相位：0.4

3. **宮位重要性**
   - 始宮（1,4,7,10）：1.0
   - 續宮（2,5,8,11）：0.8
   - 果宮（3,6,9,12）：0.6

4. **時間精確度**
   - 使用指數衰減函數
   - 基於容許度計算

5. **個人化影響**
   - 涉及重要點位加成
   - 發光體（日月）加成

## 🔧 技術實作細節

### 依賴項目

新增的依賴項目：
```yaml
fl_chart: ^0.69.0  # 圖表組件
table_calendar: ^3.0.9  # 日曆組件（已存在）
```

### 檔案結構

```
lib/
├── data/models/astrology/
│   ├── astro_event.dart (擴展)
│   ├── event_score.dart (新增)
│   ├── event_detection_config.dart (新增)
│   └── event_timeline_data.dart (新增)
├── features/astrology/
│   ├── calculations/
│   │   └── event_score_calculator.dart (新增)
│   └── services/
│       └── event_detection_service.dart (新增)
└── presentation/
    ├── pages/astrology/
    │   └── astro_event_detection_page.dart (新增)
    └── widgets/astrology/
        ├── astro_event_calendar_widget.dart (新增)
        ├── event_timeline_widget.dart (新增)
        └── event_detail_panel.dart (新增)
```

## 🚀 使用方式

### 基本使用

```dart
// 創建事件偵測頁面
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => AstroEventDetectionPage(
      birthData: userBirthData,
    ),
  ),
);
```

### 自訂配置

```dart
// 自訂事件偵測配置
final config = EventDetectionConfig(
  enabledEventTypes: {
    AstroEventType.transitAspect,
    AstroEventType.progressionAspect,
  },
  minimumEventScore: 30.0,
  detectionRangeDays: 365,
  // ... 其他配置
);

final service = EventDetectionService(
  config: config,
  astrologyService: AstrologyService(),
);
```

## 📈 效能考量

### 快取機制

1. **計算結果快取**：避免重複計算相同日期的事件
2. **UI狀態快取**：保存用戶選擇和視圖狀態
3. **分批載入**：大範圍日期分批處理

### 優化策略

1. **延遲載入**：按需計算事件資料
2. **背景計算**：使用 Isolate 進行重計算
3. **記憶體管理**：及時釋放不需要的資料

## 🧪 測試建議

### 單元測試

1. **評分算法測試**：驗證各種情況下的評分計算
2. **事件偵測測試**：測試不同推運技法的事件偵測
3. **資料模型測試**：驗證序列化/反序列化

### 整合測試

1. **UI互動測試**：測試日期選擇和面板展開
2. **主題切換測試**：驗證不同主題下的顯示效果
3. **效能測試**：測試大範圍日期的載入效能

## 🔮 未來擴展

### 功能擴展

1. **更多推運技法**：三限推運、小限法等
2. **事件提醒**：重要事件的通知功能
3. **匯出功能**：PDF報告生成
4. **分享功能**：事件分析結果分享

### UI改進

1. **動畫效果**：更豐富的過場動畫
2. **手勢操作**：縮放、拖拽等手勢支援
3. **自訂主題**：用戶自定義配色方案
4. **響應式設計**：更好的平板和桌面支援

## 📝 總結

占星事件偵測功能提供了一個完整的占星分析工具，結合了複雜的計算邏輯和直觀的視覺化介面。通過模組化的設計和完善的主題系統，為用戶提供了專業而易用的占星事件分析體驗。
