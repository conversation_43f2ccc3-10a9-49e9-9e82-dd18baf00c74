# 事件類型判斷邏輯優化記錄

## 🎯 問題描述

### 用戶反饋
用戶指出在 `_calculateEventPotential` 方法中，事件類型的判斷邏輯不夠準確：
- 只考慮了行星，沒有考慮宮位
- 在占星學中，宮位是非常關鍵的因素
- 需要同時考慮行星和宮位來做出更準確的判斷

### 原始問題代碼
```dart
// 感情事件 - 只考慮行星
if (_isRelationshipPlanet(planet1) || _isRelationshipPlanet(planet2)) {
  final sensitivity = natalAnalysis['relationship_sensitivity'] as double? ?? 50.0;
  eventTypes['relationship'] = sensitivity * 0.8;
}

// 事業事件 - 只考慮行星
if (_isCareerPlanet(planet1) || _isCareerPlanet(planet2)) {
  final sensitivity = natalAnalysis['career_sensitivity'] as double? ?? 50.0;
  eventTypes['career'] = sensitivity * 0.8;
}
```

## ✅ 優化方案

### 1. 方法簽名改進

**修正前**：
```dart
Map<String, dynamic> _calculateEventPotential(
  String planet1,
  String planet2,
  String aspectType,
  Map<String, dynamic> natalAnalysis,
)
```

**修正後**：
```dart
Map<String, dynamic> _calculateEventPotential(
  String planet1,
  String planet2,
  String aspectType,
  Map<String, dynamic> natalAnalysis, {
  int? planet1House,  // 新增宮位參數
  int? planet2House,  // 新增宮位參數
})
```

### 2. 新增宮位判斷方法

```dart
/// 判斷是否為感情相關宮位
bool _isRelationshipHouse(int? house) {
  if (house == null) return false;
  return [5, 7, 8, 11].contains(house); // 第5宮(戀愛)、第7宮(伴侶)、第8宮(親密關係)、第11宮(友情)
}

/// 判斷是否為事業相關宮位
bool _isCareerHouse(int? house) {
  if (house == null) return false;
  return [2, 6, 10].contains(house); // 第2宮(財富)、第6宮(工作)、第10宮(事業)
}

/// 判斷是否為健康相關宮位
bool _isHealthHouse(int? house) {
  if (house == null) return false;
  return [1, 6, 8, 12].contains(house); // 第1宮(體質)、第6宮(健康)、第8宮(醫療)、第12宮(慢性病)
}

/// 判斷是否為學習相關宮位
bool _isEducationHouse(int? house) {
  if (house == null) return false;
  return [3, 9].contains(house); // 第3宮(基礎學習)、第9宮(高等教育)
}

/// 判斷是否為家庭相關宮位
bool _isFamilyHouse(int? house) {
  if (house == null) return false;
  return [4, 10].contains(house); // 第4宮(家庭)、第10宮(父母)
}

/// 判斷是否為財務相關宮位
bool _isFinanceHouse(int? house) {
  if (house == null) return false;
  return [2, 8].contains(house); // 第2宮(個人財富)、第8宮(共同財產)
}

/// 判斷是否為心靈相關宮位
bool _isSpiritualHouse(int? house) {
  if (house == null) return false;
  return [8, 9, 12].contains(house); // 第8宮(轉化)、第9宮(哲學)、第12宮(靈性)
}
```

### 3. 新增行星判斷方法

```dart
/// 判斷是否為健康相關行星
bool _isHealthPlanet(String planetName) {
  return ['火星', '土星', '天王星', '冥王星', '月亮'].contains(planetName);
}

/// 判斷是否為財務相關行星
bool _isFinancePlanet(String planetName) {
  return ['金星', '木星', '土星', '冥王星'].contains(planetName);
}

/// 判斷是否為學習相關行星
bool _isEducationPlanet(String planetName) {
  return ['水星', '木星', '天王星'].contains(planetName);
}

/// 判斷是否為家庭相關行星
bool _isFamilyPlanet(String planetName) {
  return ['月亮', '太陽', '土星', '冥王星'].contains(planetName);
}

/// 判斷是否為心靈相關行星
bool _isSpiritualPlanet(String planetName) {
  return ['海王星', '冥王星', '木星', '月亮'].contains(planetName);
}
```

### 4. 優化事件判斷邏輯

**感情事件範例**：
```dart
// 感情事件 - 考慮行星和宮位
final isRelationshipEvent = _isRelationshipPlanet(planet1) || 
                           _isRelationshipPlanet(planet2) ||
                           _isRelationshipHouse(planet1House) ||
                           _isRelationshipHouse(planet2House);

if (isRelationshipEvent) {
  final sensitivity = natalAnalysis['relationship_sensitivity'] as double? ?? 50.0;
  double multiplier = 0.8;
  
  // 如果同時涉及感情行星和感情宮位，增加權重
  if ((_isRelationshipPlanet(planet1) || _isRelationshipPlanet(planet2)) &&
      (_isRelationshipHouse(planet1House) || _isRelationshipHouse(planet2House))) {
    multiplier = 1.2;
  }
  
  eventTypes['relationship'] = sensitivity * multiplier;
}
```

### 5. 更新方法調用

**修正前**：
```dart
'event_potential': _calculateEventPotential(
  aspect.planet1.name,
  aspect.planet2.name,
  aspectType,
  natalAnalysis,
),
```

**修正後**：
```dart
'event_potential': _calculateEventPotential(
  aspect.planet1.name,
  aspect.planet2.name,
  aspectType,
  natalAnalysis,
  planet1House: aspect.planet1.house,
  planet2House: aspect.planet2.house,
),
```

## 🎯 優化特點

### 1. 綜合判斷邏輯
- **行星 OR 宮位**：只要涉及相關行星或宮位就觸發事件類型
- **行星 AND 宮位**：同時涉及相關行星和宮位時增加權重
- **權重調整**：根據行星宮位組合調整事件強度

### 2. 事件類型擴展
- **感情事件**：金星、月亮、火星 + 第5、7、8、11宮
- **事業事件**：太陽、土星、木星 + 第2、6、10宮
- **健康事件**：火星、土星、天王星、冥王星、月亮 + 第1、6、8、12宮
- **財務事件**：金星、木星、土星、冥王星 + 第2、8宮
- **學習事件**：水星、木星、天王星 + 第3、9宮
- **家庭事件**：月亮、太陽、土星、冥王星 + 第4、10宮
- **心靈事件**：海王星、冥王星、木星、月亮 + 第8、9、12宮

### 3. 權重系統
- **基礎權重**：0.7-0.9（根據事件類型調整）
- **組合權重**：1.1-1.3（行星+宮位組合時）
- **敏感度調整**：基於本命盤分析的個人敏感度

## 📊 優化效果

### 修正前的問題
- ❌ 只考慮行星，忽略宮位
- ❌ 事件類型判斷不夠精確
- ❌ 缺少多種生活領域的事件類型
- ❌ 權重系統過於簡單

### 修正後的改善
- ✅ 同時考慮行星和宮位
- ✅ 事件類型判斷更加精確
- ✅ 涵蓋七大生活領域的事件類型
- ✅ 智能權重調整系統

## 🔧 占星學理論基礎

### 宮位系統
- **第1宮**：自我、體質、外貌
- **第2宮**：個人財富、價值觀
- **第3宮**：溝通、學習、兄弟姊妹
- **第4宮**：家庭、根基、母親
- **第5宮**：戀愛、創造、子女
- **第6宮**：工作、健康、服務
- **第7宮**：伴侶、合作、公開敵人
- **第8宮**：共同財產、轉化、死亡
- **第9宮**：高等教育、哲學、遠行
- **第10宮**：事業、聲譽、父親
- **第11宮**：友情、團體、願望
- **第12宮**：潛意識、靈性、隱藏

### 行星意義
- **太陽**：自我、意志、父親
- **月亮**：情感、直覺、母親
- **水星**：溝通、思維、學習
- **金星**：愛情、美感、價值
- **火星**：行動、衝動、競爭
- **木星**：擴展、幸運、智慧
- **土星**：限制、責任、結構
- **天王星**：革新、突變、自由
- **海王星**：夢想、靈性、迷惑
- **冥王星**：轉化、重生、權力

## 🔄 後續建議

1. **測試驗證**：為新的判斷邏輯編寫單元測試
2. **性能優化**：考慮快取宮位判斷結果
3. **功能擴展**：可以考慮添加更細緻的事件子類型
4. **用戶反饋**：收集用戶對新判斷邏輯準確性的反饋

## 📝 總結

這次優化大幅提升了事件類型判斷的準確性：
- 從單純的行星判斷升級為行星+宮位的綜合判斷
- 擴展了事件類型覆蓋範圍，涵蓋七大生活領域
- 建立了智能權重調整系統
- 符合傳統占星學理論基礎

優化後的系統能夠更準確地識別和分類占星事件，為用戶提供更精確的事件預測和分析。
