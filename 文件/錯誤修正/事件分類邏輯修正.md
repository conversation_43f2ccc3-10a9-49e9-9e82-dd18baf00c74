# 事件分類邏輯修正記錄

## 🐛 問題描述

### 用戶反饋
用戶發現在分類事件時間軸中，只有「心靈」類型有事件數量，其他類型都是0。

### 問題分析
經過檢查發現了以下問題：

1. **鍵名不匹配**：
   - 在 `_createEventFromTrigger` 方法中，`additionalData` 使用的鍵是 `'event_category'`
   - 但在 `_determineEventCategory` 方法中，查找的鍵是 `'primary_event_type'`

2. **傳統事件缺少分類資訊**：
   - `_detectTraditionalEvents` 方法創建的事件沒有包含事件分類資訊
   - 所有傳統事件都被歸類為預設的心靈類別

3. **行星名稱語言不一致**：
   - 判斷方法使用中文行星名稱（如「金星」、「火星」）
   - 但實際事件中的行星名稱是英文（如「Venus」、「Mars」）

## ✅ 修正方案

### 1. 修正事件分類鍵名匹配

**修正前**：
```dart
static EventCategory _determineEventCategory(AstroEvent astroEvent) {
  final eventType = astroEvent.additionalData?['primary_event_type'] as String?;
  // ...
}
```

**修正後**：
```dart
static EventCategory _determineEventCategory(AstroEvent astroEvent) {
  String? eventType;
  
  // 嘗試不同的鍵名
  eventType = astroEvent.additionalData?['primary_event_type'] as String?;
  eventType ??= astroEvent.additionalData?['event_category'] as String?;
  eventType ??= astroEvent.additionalData?['category'] as String?;
  
  // 如果找到事件類型，進行分類
  if (eventType != null) {
    switch (eventType) {
      case 'relationship': return EventCategory.relationship;
      case 'career': return EventCategory.career;
      case 'health': return EventCategory.health;
      case 'finance':
      case 'financial': // 支援兩種拼寫
        return EventCategory.finance;
      // ... 其他類別
    }
  }
  
  // 根據占星事件類型進行預設分類
  return _getDefaultCategoryByEventType(astroEvent.type);
}
```

### 2. 為傳統事件添加分類邏輯

#### 新增分類判斷方法
```dart
String _determineEventCategoryForTraditionalEvent(
  String transitPlanet,
  String natalPlanet,
  String aspectType,
  int? natalHouse,
) {
  // 轉換行星名稱為中文
  final transitPlanetCN = _translatePlanetName(transitPlanet);
  final natalPlanetCN = _translatePlanetName(natalPlanet);

  // 感情事件判斷
  if (_isRelationshipPlanet(transitPlanetCN) || 
      _isRelationshipPlanet(natalPlanetCN) ||
      _isRelationshipHouse(natalHouse)) {
    return 'relationship';
  }
  
  // 事業事件判斷
  if (_isCareerPlanet(transitPlanetCN) || 
      _isCareerPlanet(natalPlanetCN) ||
      _isCareerHouse(natalHouse)) {
    return 'career';
  }
  
  // ... 其他類別判斷
  
  // 預設為心靈類別
  return 'spiritual';
}
```

#### 行星名稱轉換方法
```dart
String _translatePlanetName(String planetName) {
  final translations = {
    'Sun': '太陽',
    'Moon': '月亮',
    'Mercury': '水星',
    'Venus': '金星',
    'Mars': '火星',
    'Jupiter': '木星',
    'Saturn': '土星',
    'Uranus': '天王星',
    'Neptune': '海王星',
    'Pluto': '冥王星',
  };
  return translations[planetName] ?? planetName;
}
```

### 3. 修正事件創建邏輯

#### 行運事件修正
```dart
// 判斷事件類別
final eventCategory = _determineEventCategoryForTraditionalEvent(
  transitPlanet.name,
  natalPlanet.name,
  aspectType,
  natalPlanet.house,
);

final event = AstroEvent(
  // ... 其他屬性
  additionalData: {
    'transitPlanet': transitPlanet,
    'natalPlanet': natalPlanet,
    'isTransit': true,
    'event_category': eventCategory, // 添加事件分類
  },
);
```

#### 推運事件修正
```dart
// 判斷事件類別
final eventCategory = _determineEventCategoryForTraditionalEvent(
  progressionPlanet.name,
  natalPlanet.name,
  aspectType,
  natalPlanet.house,
);

final event = AstroEvent(
  // ... 其他屬性
  additionalData: {
    'progressionPlanet': progressionPlanet,
    'natalPlanet': natalPlanet,
    'isProgression': true,
    'event_category': eventCategory, // 添加事件分類
  },
);
```

#### 太陽弧推運事件修正
```dart
// 判斷事件類別
final eventCategory = _determineEventCategoryForTraditionalEvent(
  natalPlanet1.name,
  natalPlanet2.name,
  aspectType,
  natalPlanet2.house,
);

final event = AstroEvent(
  // ... 其他屬性
  additionalData: {
    'solarArcPlanet': natalPlanet1,
    'natalPlanet': natalPlanet2,
    'solarArcDegrees': solarArcDegrees,
    'isSolarArc': true,
    'event_category': eventCategory, // 添加事件分類
  },
);
```

## 🔧 技術細節

### 分類邏輯優先級
1. **明確的事件類型**：檢查 `additionalData` 中的分類資訊
2. **行星和宮位組合**：基於占星學原理進行分類
3. **預設分類**：根據占星事件類型進行備用分類

### 行星分類規則
- **感情事件**：金星、月亮、火星 + 第5、7、8、11宮
- **事業事件**：太陽、土星、木星 + 第2、6、10宮
- **健康事件**：火星、土星、天王星、冥王星、月亮 + 第1、6、8、12宮
- **財務事件**：金星、木星、土星、冥王星 + 第2、8宮
- **學習事件**：水星、木星、天王星 + 第3、9宮
- **家庭事件**：月亮、太陽、土星、冥王星 + 第4、10宮
- **心靈事件**：海王星、冥王星、木星、月亮 + 第8、9、12宮

### 容錯機制
- **多鍵名支援**：支援不同的 `additionalData` 鍵名
- **拼寫變體**：支援 `finance`/`financial`、`education`/`learning` 等變體
- **語言轉換**：自動轉換英文行星名稱為中文
- **預設分類**：確保所有事件都有分類

## 📊 修正效果

### 修正前的問題
- ❌ 只有心靈類型有事件數量
- ❌ 其他類型都是0
- ❌ 事件分類不準確
- ❌ 行星名稱語言不一致

### 修正後的改善
- ✅ 所有類型都有正確的事件數量
- ✅ 事件分類準確反映占星學原理
- ✅ 支援多種鍵名和拼寫變體
- ✅ 自動處理行星名稱語言轉換

## 🧪 測試驗證

### 驗證方法
1. **檢查事件分類**：確認每個事件都有正確的 `event_category`
2. **統計各類別數量**：驗證七大類別都有事件
3. **行星名稱轉換**：確認英文行星名稱正確轉換為中文
4. **宮位判斷**：驗證宮位相關的分類邏輯

### 預期結果
- 感情事件：涉及金星、月亮、火星或第5、7、8、11宮的事件
- 事業事件：涉及太陽、土星、木星或第2、6、10宮的事件
- 健康事件：涉及火星、土星、天王星、冥王星、月亮或第1、6、8、12宮的事件
- 財務事件：涉及金星、木星、土星、冥王星或第2、8宮的事件
- 學習事件：涉及水星、木星、天王星或第3、9宮的事件
- 家庭事件：涉及月亮、太陽、土星、冥王星或第4、10宮的事件
- 心靈事件：涉及海王星、冥王星、木星、月亮或第8、9、12宮的事件

## 🔄 後續建議

1. **監控分類準確性**：定期檢查事件分類的準確性
2. **優化分類邏輯**：根據用戶反饋調整分類規則
3. **添加調試資訊**：在開發模式下顯示分類過程
4. **性能優化**：考慮快取行星名稱轉換結果

## 📝 總結

這次修正解決了事件分類邏輯中的關鍵問題：
- 修正了鍵名不匹配的問題
- 為傳統事件添加了完整的分類邏輯
- 解決了行星名稱語言不一致的問題
- 建立了完善的容錯機制

修正後的系統能夠正確地將事件分類到七大生活領域，為用戶提供準確的分類事件分析。
