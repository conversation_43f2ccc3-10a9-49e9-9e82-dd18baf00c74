# 占星事件偵測功能測試指南

## 📋 測試概述

本指南提供占星事件偵測功能的完整測試方案，包括單元測試、整合測試、UI測試和效能測試。

## 🧪 單元測試

### 1. 事件評分計算器測試

#### 測試檔案：`test/features/astrology/calculations/event_score_calculator_test.dart`

```dart
import 'package:flutter_test/flutter_test.dart';
import 'package:astreal/features/astrology/calculations/event_score_calculator.dart';

void main() {
  group('EventScoreCalculator', () {
    late EventScoreCalculator calculator;
    
    setUp(() {
      calculator = EventScoreCalculator(
        config: EventScoreConfig.defaultConfig(),
      );
    });
    
    test('應該正確計算相位事件評分', () {
      // 測試相位評分計算
    });
    
    test('應該正確計算行星換座評分', () {
      // 測試行星換座評分
    });
    
    test('應該正確計算行星換宮評分', () {
      // 測試行星換宮評分
    });
  });
}
```

#### 測試重點

1. **評分算法正確性**
   - 驗證不同行星組合的權重計算
   - 驗證相位強度計算
   - 驗證宮位重要性計算
   - 驗證時間精確度計算

2. **邊界條件測試**
   - 最大/最小容許度
   - 極端分數值
   - 空值處理

3. **個人化影響測試**
   - 重要點位加成
   - 發光體加成
   - 行運vs推運差異

### 2. 事件偵測服務測試

#### 測試檔案：`test/features/astrology/services/event_detection_service_test.dart`

```dart
void main() {
  group('EventDetectionService', () {
    test('應該正確偵測行運事件', () {
      // 測試行運事件偵測
    });
    
    test('應該正確偵測推運事件', () {
      // 測試推運事件偵測
    });
    
    test('應該正確過濾低分事件', () {
      // 測試事件篩選
    });
    
    test('應該正確處理日期範圍', () {
      // 測試日期範圍處理
    });
  });
}
```

### 3. 資料模型測試

#### 測試檔案：`test/data/models/astrology/event_models_test.dart`

```dart
void main() {
  group('Event Models', () {
    test('AstroEvent 序列化/反序列化', () {
      // 測試 toMap/fromMap
    });
    
    test('EventScore 計算邏輯', () {
      // 測試評分邏輯
    });
    
    test('EventTimelineData 統計計算', () {
      // 測試統計計算
    });
  });
}
```

### 4. 快取服務測試

#### 測試檔案：`test/features/astrology/services/event_cache_service_test.dart`

```dart
void main() {
  group('EventCacheService', () {
    test('應該正確保存和讀取快取', () {
      // 測試快取讀寫
    });
    
    test('應該正確處理過期快取', () {
      // 測試過期處理
    });
    
    test('應該正確清理快取', () {
      // 測試快取清理
    });
  });
}
```

## 🔗 整合測試

### 1. 端到端事件偵測測試

#### 測試檔案：`integration_test/event_detection_integration_test.dart`

```dart
void main() {
  group('事件偵測整合測試', () {
    testWidgets('完整的事件偵測流程', (tester) async {
      // 1. 載入測試資料
      // 2. 執行事件偵測
      // 3. 驗證結果
      // 4. 測試快取功能
    });
  });
}
```

### 2. UI整合測試

#### 測試檔案：`integration_test/ui_integration_test.dart`

```dart
void main() {
  group('UI整合測試', () {
    testWidgets('年曆熱度圖互動', (tester) async {
      // 測試日期點擊、主題切換等
    });
    
    testWidgets('時間軸圖表互動', (tester) async {
      // 測試數據點點擊、縮放等
    });
    
    testWidgets('事件詳情面板', (tester) async {
      // 測試面板展開、關閉、內容顯示
    });
  });
}
```

## 🎨 UI測試

### 1. Widget測試

#### 年曆熱度圖測試

```dart
void main() {
  group('AstroEventCalendarWidget', () {
    testWidgets('應該正確顯示事件強度', (tester) async {
      // 測試顏色強度顯示
    });
    
    testWidgets('應該正確響應日期點擊', (tester) async {
      // 測試點擊回調
    });
    
    testWidgets('應該正確切換主題', (tester) async {
      // 測試主題切換
    });
  });
}
```

#### 時間軸圖表測試

```dart
void main() {
  group('EventTimelineWidget', () {
    testWidgets('應該正確顯示數據點', (tester) async {
      // 測試數據點顯示
    });
    
    testWidgets('應該正確顯示工具提示', (tester) async {
      // 測試工具提示
    });
  });
}
```

#### 事件詳情面板測試

```dart
void main() {
  group('EventDetailPanel', () {
    testWidgets('應該正確顯示事件詳情', (tester) async {
      // 測試事件詳情顯示
    });
    
    testWidgets('應該正確播放動畫', (tester) async {
      // 測試動畫效果
    });
  });
}
```

### 2. 主題測試

```dart
void main() {
  group('主題測試', () {
    testWidgets('Starmaster主題顯示正確', (tester) async {
      // 測試專業模式主題
    });
    
    testWidgets('Starlight主題顯示正確', (tester) async {
      // 測試初心者模式主題
    });
    
    testWidgets('深色模式顯示正確', (tester) async {
      // 測試深色模式
    });
  });
}
```

## ⚡ 效能測試

### 1. 計算效能測試

#### 測試檔案：`test/performance/calculation_performance_test.dart`

```dart
void main() {
  group('計算效能測試', () {
    test('大範圍日期偵測效能', () async {
      final stopwatch = Stopwatch()..start();
      
      // 執行一年範圍的事件偵測
      await eventDetectionService.detectEvents(
        testBirthData,
        DateTime(2024, 1, 1),
        DateTime(2024, 12, 31),
      );
      
      stopwatch.stop();
      
      // 驗證執行時間在合理範圍內
      expect(stopwatch.elapsedMilliseconds, lessThan(30000)); // 30秒內
    });
    
    test('快取效能測試', () async {
      // 測試快取讀寫效能
    });
  });
}
```

### 2. 記憶體使用測試

```dart
void main() {
  group('記憶體使用測試', () {
    test('長時間運行記憶體穩定性', () async {
      // 測試記憶體洩漏
    });
    
    test('大量資料處理記憶體使用', () async {
      // 測試大量資料處理
    });
  });
}
```

### 3. UI效能測試

```dart
void main() {
  group('UI效能測試', () {
    testWidgets('滾動效能測試', (tester) async {
      // 測試列表滾動流暢度
    });
    
    testWidgets('動畫效能測試', (tester) async {
      // 測試動畫流暢度
    });
  });
}
```

## 🔍 測試資料準備

### 1. 測試用出生資料

```dart
class TestData {
  static final BirthData testBirthData1 = BirthData(
    name: '測試用戶1',
    birthDateTime: DateTime(1990, 6, 15, 14, 30),
    latitude: 25.0330,
    longitude: 121.5654,
    // ... 其他欄位
  );
  
  static final BirthData testBirthData2 = BirthData(
    name: '測試用戶2',
    birthDateTime: DateTime(1985, 12, 25, 9, 15),
    latitude: 40.7128,
    longitude: -74.0060,
    // ... 其他欄位
  );
}
```

### 2. 模擬事件資料

```dart
class MockEventData {
  static List<AstroEvent> generateMockEvents(DateTime date) {
    // 生成模擬事件資料
  }
  
  static EventTimelineData generateMockTimeline(
    DateTime start,
    DateTime end,
  ) {
    // 生成模擬時間軸資料
  }
}
```

## 🚀 測試執行

### 1. 單元測試執行

```bash
# 執行所有單元測試
flutter test

# 執行特定測試檔案
flutter test test/features/astrology/calculations/event_score_calculator_test.dart

# 執行測試並生成覆蓋率報告
flutter test --coverage
```

### 2. 整合測試執行

```bash
# 執行整合測試
flutter test integration_test/

# 在特定設備上執行
flutter test integration_test/ -d <device_id>
```

### 3. 效能測試執行

```bash
# 執行效能測試
flutter test test/performance/

# 使用 profile 模式執行
flutter test --profile test/performance/
```

## 📊 測試報告

### 1. 覆蓋率要求

- **單元測試覆蓋率**：≥ 90%
- **整合測試覆蓋率**：≥ 80%
- **關鍵路徑覆蓋率**：100%

### 2. 效能基準

- **事件偵測速度**：1年範圍 ≤ 30秒
- **UI響應時間**：≤ 100ms
- **記憶體使用**：≤ 200MB
- **快取命中率**：≥ 80%

### 3. 測試報告生成

```bash
# 生成測試報告
flutter test --reporter=json > test_results.json

# 生成覆蓋率報告
genhtml coverage/lcov.info -o coverage/html
```

## 🐛 常見問題與解決方案

### 1. 測試環境問題

**問題**：Swiss Ephemeris 在測試環境中初始化失敗
**解決方案**：使用模擬資料或跳過需要 Swiss Ephemeris 的測試

### 2. 非同步測試問題

**問題**：非同步操作測試不穩定
**解決方案**：使用 `pumpAndSettle()` 等待所有動畫完成

### 3. 快取測試問題

**問題**：快取測試互相影響
**解決方案**：每個測試前清理快取狀態

## 📝 測試最佳實踐

1. **測試隔離**：每個測試應該獨立，不依賴其他測試
2. **資料清理**：測試後清理產生的資料
3. **模擬外部依賴**：使用 Mock 隔離外部服務
4. **測試命名**：使用描述性的測試名稱
5. **測試文檔**：為複雜測試添加註釋說明

## 🎯 測試檢查清單

- [ ] 所有單元測試通過
- [ ] 整合測試通過
- [ ] UI測試通過
- [ ] 效能測試達標
- [ ] 覆蓋率達標
- [ ] 記憶體洩漏檢查
- [ ] 不同設備測試
- [ ] 不同主題測試
- [ ] 邊界條件測試
- [ ] 錯誤處理測試
