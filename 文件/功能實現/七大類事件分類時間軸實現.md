# 七大類事件分類時間軸實現記錄

## 🎯 需求描述

### 用戶需求
用戶要求事件分數時間軸應該：
1. **區分七大類人生重大事件**：感情、事業、健康、財務、學習、家庭、心靈
2. **用不同的線來畫**：每個類別使用不同顏色的線條
3. **可以篩選星盤類型與事件類型**：支援動態篩選功能

### 設計目標
- 提供更詳細的事件分類視覺化
- 支援多線圖表顯示
- 提供靈活的篩選功能
- 保持良好的用戶體驗

## ✅ 實現方案

### 1. 新增資料模型

#### EventCategory 枚舉
```dart
enum EventCategory {
  relationship, // 感情
  career,      // 事業
  health,      // 健康
  finance,     // 財務
  education,   // 學習
  family,      // 家庭
  spiritual,   // 心靈
}
```

**特點**：
- 每個類別都有對應的中文名稱、顏色和圖標
- 感情：粉紅色 + 愛心圖標
- 事業：藍色 + 工作圖標
- 健康：綠色 + 健康圖標
- 財務：橙色 + 金錢圖標
- 學習：紫色 + 學校圖標
- 家庭：棕色 + 家庭圖標
- 心靈：藍灰色 + 自我提升圖標

#### CategorizedEventScore 模型
```dart
class CategorizedEventScore {
  final DateTime date;
  final Map<EventCategory, double> categoryScores;
  final Map<EventCategory, int> categoryCounts;
  final Map<EventCategory, List<EventScore>> categoryEvents;
  final double totalScore;
  final int totalEventCount;
}
```

**功能**：
- 儲存每日各類別的分數和事件數量
- 支援從基本事件列表自動分類
- 提供類別查詢和統計方法

#### CategorizedTimelineData 模型
```dart
class CategorizedTimelineData {
  final DateTime startDate;
  final DateTime endDate;
  final List<CategorizedEventScore> dailyScores;
  final List<AstroEvent> allEvents;
  final Map<EventCategory, double> maxScores;
  final Map<EventCategory, double> averageScores;
  final Map<EventCategory, int> totalEventCounts;
}
```

**功能**：
- 管理整個時間範圍的分類事件資料
- 提供各類別的統計資訊
- 支援時間序列資料查詢

### 2. 事件分類邏輯

#### 自動分類算法
```dart
static EventCategory _determineEventCategory(AstroEvent astroEvent) {
  // 1. 優先檢查 additionalData 中的事件類型
  final eventType = astroEvent.additionalData?['primary_event_type'] as String?;
  
  // 2. 根據明確的事件類型分類
  switch (eventType) {
    case 'relationship': return EventCategory.relationship;
    case 'career': return EventCategory.career;
    // ... 其他類別
  }
  
  // 3. 根據占星事件類型進行預設分類
  return _getDefaultCategoryByEventType(astroEvent.type);
}
```

**分類規則**：
- **優先級1**：使用事件的 `primary_event_type` 屬性
- **優先級2**：根據占星事件類型進行預設分類
- **備用方案**：預設歸類為心靈類別

### 3. 圖表組件實現

#### CategorizedEventTimelineWidget
```dart
class CategorizedEventTimelineWidget extends StatefulWidget {
  final CategorizedTimelineData timelineData;
  final Function(DateTime, CategorizedEventScore?)? onDataPointTapped;
  final EventTimelineFilter initialFilter;
}
```

**核心功能**：
- **多線圖表**：每個類別使用獨特顏色的線條
- **動態篩選**：可選擇顯示/隱藏特定類別
- **總分線**：可選顯示所有類別的總分趨勢
- **互動功能**：點擊數據點顯示詳細資訊

#### EventTimelineFilter 篩選器
```dart
class EventTimelineFilter {
  final Set<EventCategory> enabledCategories;
  final Set<String> enabledChartTypes;
  final bool showTotalScore;
}
```

**篩選功能**：
- **事件類別篩選**：可選擇顯示的事件類別
- **星盤類型篩選**：支援行運、推運、太陽弧等
- **總分線開關**：可控制是否顯示總分趨勢

### 4. UI 設計特點

#### 篩選器控制面板
- **FilterChip 設計**：每個類別顯示為可選擇的標籤
- **事件數量顯示**：標籤上顯示該類別的事件總數
- **顏色標識**：使用類別對應的顏色和圖標
- **禁用狀態**：沒有事件的類別顯示為灰色不可選

#### 圖表視覺化
- **多色線條**：每個類別使用專屬顏色
- **虛線總分**：總分線使用黑色虛線顯示
- **數據點標記**：每條線上的數據點清晰可見
- **工具提示**：懸停顯示詳細的分類資訊

#### 圖例系統
- **線條圖例**：顯示每個類別的顏色和名稱
- **圖標標識**：配合類別專屬圖標
- **總分標識**：虛線樣式的總分圖例

### 5. 服務層整合

#### EventDetectionService 擴展
```dart
Future<CategorizedTimelineData> detectCategorizedEvents(
  BirthData birthData,
  DateTime startDate,
  DateTime endDate, {
  bool useCache = true,
}) async {
  // 1. 獲取基本事件資料
  final basicTimelineData = await detectEvents(...);
  
  // 2. 轉換為分類事件資料
  final categorizedDailyScores = <CategorizedEventScore>[];
  
  // 3. 為每一天創建分類評分
  for (final dailyScore in basicTimelineData.dailyScores) {
    final categorizedScore = CategorizedEventScore.fromEvents(...);
    categorizedDailyScores.add(categorizedScore);
  }
  
  // 4. 創建分類時間軸資料
  return CategorizedTimelineData.fromDailyScores(...);
}
```

#### 頁面整合
- **新增第三個標籤**：「分類時間軸」
- **狀態管理**：同時管理基本和分類事件資料
- **事件處理**：支援分類事件的點擊回調

## 🎨 視覺設計

### 顏色方案
| 類別 | 顏色 | 色碼 | 圖標 |
|------|------|------|------|
| 感情 | 粉紅色 | #E91E63 | favorite |
| 事業 | 藍色 | #2196F3 | work |
| 健康 | 綠色 | #4CAF50 | health_and_safety |
| 財務 | 橙色 | #FF9800 | attach_money |
| 學習 | 紫色 | #9C27B0 | school |
| 家庭 | 棕色 | #795548 | family_restroom |
| 心靈 | 藍灰色 | #607D8B | self_improvement |

### 線條樣式
- **類別線條**：實線，寬度2px，帶數據點
- **總分線條**：虛線，寬度3px，無數據點
- **數據點**：圓形，半徑3px，白色邊框

## 🔧 技術特點

### 性能優化
- **資料快取**：重用基本事件資料進行分類
- **按需渲染**：只渲染啟用的類別線條
- **記憶體管理**：合理的資料結構設計

### 擴展性
- **模組化設計**：篩選器、圖表、資料模型分離
- **配置靈活**：支援自訂篩選器和主題
- **向後兼容**：不影響現有的基本時間軸功能

### 用戶體驗
- **直觀操作**：點擊標籤即可篩選
- **即時反饋**：篩選結果立即更新
- **資訊豐富**：工具提示顯示詳細資訊

## 📊 功能對比

### 修正前
- ❌ 只有單一總分線
- ❌ 無法區分事件類別
- ❌ 缺少篩選功能
- ❌ 資訊展示有限

### 修正後
- ✅ 七大類別多線顯示
- ✅ 清晰的事件分類
- ✅ 靈活的篩選功能
- ✅ 豐富的視覺化資訊

## 🔄 後續建議

1. **性能優化**：考慮大資料量時的渲染優化
2. **功能擴展**：可以添加更多篩選條件
3. **用戶自訂**：允許用戶自訂類別顏色
4. **匯出功能**：支援分類資料的匯出

## 📝 總結

這次實現成功地將單一的事件分數時間軸升級為功能豐富的分類時間軸系統：

- **視覺化提升**：從單線圖表升級為多線分類圖表
- **功能增強**：添加了完整的篩選和互動功能
- **資料豐富**：提供了詳細的分類統計資訊
- **用戶體驗**：直觀的操作界面和豐富的視覺反饋

新的分類時間軸為用戶提供了更深入、更細緻的占星事件分析工具，大大提升了應用的專業性和實用性。
